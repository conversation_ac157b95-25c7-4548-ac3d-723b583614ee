"""
API routes for the Legal RAG API.

This module contains all the FastAPI route definitions for the legal
document querying and management endpoints.
"""

import uuid
import logging
import time

from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse

from app.models import (
    ChatRequest, ChatResponse, ChatErrorResponse, HealthResponse, 
    IngestRequest, IngestResponse, DocumentIngestRequest, SystemStats
)
from app.services import RAGService

logger = logging.getLogger(__name__)

# ============================================================================
# Router Setup
# ============================================================================

api_router = APIRouter()
admin_router = APIRouter(prefix="/admin")


# ============================================================================
# Dependencies
# ============================================================================

def get_rag_service() -> RAGService:
    """Dependency to get RAG service instance."""
    return RAGService()


# ============================================================================
# Public API Routes
# ============================================================================

@api_router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint for API status."""
    return HealthResponse(status="healthy")


@api_router.post("/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    rag_service: RAGService = Depends(get_rag_service)
):
    """
    Main chat endpoint for legal queries.
    
    This endpoint processes legal questions through the RAG pipeline:
    1. Searches for relevant legal documents
    2. Generates contextual responses using LLM
    3. Returns both answer and source documents
    """
    start_time = time.time()
    conversation_id = request.conversation_id or str(uuid.uuid4())
    
    try:
        # Validate input
        if not request.query.strip():
            return JSONResponse(
                status_code=400,
                content=ChatErrorResponse(
                    error="INVALID_QUERY",
                    message="Query cannot be empty",
                    conversation_id=conversation_id
                ).dict()
            )
        
        # Process the query with timing
        logger.info(f"Processing query: {request.query[:100]}...")
        answer, sources = rag_service.query(request.query)
        
        processing_time = time.time() - start_time
        
        return ChatResponse(
            answer=answer,
            sources=sources,
            conversation_id=conversation_id,
            query=request.query,
            processing_time=processing_time
        )
    
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Error in chat endpoint: {e}")
        
        return JSONResponse(
            status_code=500,
            content=ChatErrorResponse(
                error="PROCESSING_ERROR",
                message="Failed to process your query. Please try again.",
                conversation_id=conversation_id
            ).dict()
        )


@api_router.get("/search")
async def search_documents(
    query: str,
    top_k: int = 5,
    rag_service: RAGService = Depends(get_rag_service)
):
    """
    Search for documents without LLM response.
    
    This endpoint performs vector search only and returns raw search results
    without generating an LLM response.
    """
    try:
        if not query.strip():
            raise HTTPException(status_code=400, detail="Query cannot be empty")
        
        results = rag_service.search_only(query, top_k)
        
        return {
            "query": query,
            "results": results,
            "total_results": len(results)
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in search endpoint: {e}")
        raise HTTPException(status_code=500, detail="Search failed")


# ============================================================================
# Admin Routes
# ============================================================================

@admin_router.post("/ingest", response_model=IngestResponse)
async def ingest_documents(
    request: IngestRequest,
):
    """
    Admin endpoint to ingest new documents from file path.
    
    This endpoint is a placeholder for document ingestion from file paths.
    In a production environment, this would handle file uploads and processing.
    """
    try:
        # This would be called by your data pipeline
        # For now, return a placeholder response
        return IngestResponse(
            message=f"Document ingestion started for {request.file_path}",
            chunks_created=0,
            success=True
        )
    
    except Exception as e:
        logger.error(f"Error in ingest endpoint: {e}")
        raise HTTPException(status_code=500, detail="Ingestion failed")


@admin_router.post("/ingest/documents", response_model=IngestResponse)
async def ingest_document_list(
    request: DocumentIngestRequest,
    rag_service: RAGService = Depends(get_rag_service)
):
    """
    Admin endpoint to ingest documents directly.
    
    This endpoint accepts a list of documents with content and metadata
    and ingests them directly into the vector database.
    """
    try:
        if not request.documents:
            raise HTTPException(status_code=400, detail="No documents provided")
        
        # Ingest documents into Pinecone
        success = rag_service.ingest_documents(request.documents)
        
        if success:
            return IngestResponse(
                message=f"Successfully ingested {len(request.documents)} documents",
                chunks_created=len(request.documents),
                documents_processed=len(request.documents),
                success=True
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to ingest documents")
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in document ingest endpoint: {e}")
        raise HTTPException(status_code=500, detail="Ingestion failed")


@admin_router.get("/stats", response_model=SystemStats)
async def get_stats(
    rag_service: RAGService = Depends(get_rag_service)
):
    """
    Get system statistics.
    
    Returns information about the vector database including total vectors,
    dimension, and index fullness.
    """
    try:
        stats = rag_service.get_index_stats()
        
        return SystemStats(
            total_vectors=stats.get('total_vector_count', 0),
            index_dimension=stats.get('dimension', 384),
            index_fullness=stats.get('index_fullness', 0.0),
            last_updated=None  # You can add this if you track it
        )
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get stats")