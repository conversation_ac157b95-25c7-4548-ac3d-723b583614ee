# app/routes.py
from fastapi import APIRouter, HTTPException, Depends
import uuid
import logging

from app.models import (
    ChatRequest, ChatResponse, HealthResponse, 
    IngestRequest, IngestResponse
)
from app.services import RAGService

logger = logging.getLogger(__name__)

# Create routers
api_router = APIRouter()
admin_router = APIRouter(prefix="/admin")

# Dependency to get RAG service
def get_rag_service() -> RAGService:
    return RAGService()


@api_router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(status="healthy")


@api_router.post("/chat", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    rag_service: RAGService = Depends(get_rag_service)
):
    """Main chat endpoint for legal queries"""
    try:
        # Generate conversation ID if not provided
        conversation_id = request.conversation_id or str(uuid.uuid4())
        
        # Process the query
        answer, sources = rag_service.query(request.query)
        
        return ChatResponse(
            answer=answer,
            sources=sources,
            conversation_id=conversation_id
        )
    
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@admin_router.post("/ingest", response_model=IngestResponse)
async def ingest_documents(
    request: IngestRequest,
    rag_service: RAGService = Depends(get_rag_service)
):
    """Admin endpoint to ingest new documents"""
    try:
        # This would be called by your data pipeline
        # For now, return a placeholder response
        return IngestResponse(
            message=f"Document ingestion started for {request.file_path}",
            chunks_created=0,
            success=True
        )
    
    except Exception as e:
        logger.error(f"Error in ingest endpoint: {e}")
        raise HTTPException(status_code=500, detail="Ingestion failed")


@admin_router.get("/stats")
async def get_stats():
    """Get system statistics"""
    try:
        # Add your stats logic here
        return {
            "total_documents": "N/A",
            "index_status": "healthy",
            "last_updated": "N/A"
        }
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get stats")