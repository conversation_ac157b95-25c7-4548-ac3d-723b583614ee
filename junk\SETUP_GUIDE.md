# Setup Guide - Fix Installation Issues

## 🚨 Current Issues

You're experiencing dependency installation problems with Python 3.13. Here's how to fix them:

## 🔧 Step-by-Step Fix

### 1. Install Dependencies Step by Step

Run the automated installation script:

```bash
python install_dependencies.py
```

This script will:
- Upgrade pip to the latest version
- Install packages in the correct order to avoid conflicts
- Use compatible versions for Python 3.13

### 2. Manual Installation (if automated fails)

If the automated script fails, install manually:

```bash
# Upgrade pip first
python -m pip install --upgrade pip

# Install core dependencies
pip install numpy>=1.26.0
pip install pydantic>=2.5.0
pip install pydantic-settings>=2.1.0
pip install python-dotenv>=1.0.0

# Install ML/AI dependencies
pip install sentence-transformers>=2.2.2
pip install google-generativeai>=0.3.2
pip install pinecone-client>=2.2.4

# Install web framework
pip install fastapi>=0.104.1
pip install uvicorn>=0.24.0
pip install httpx>=0.25.2

# Install document processing
pip install PyPDF2>=3.0.1
pip install python-docx>=1.1.0
pip install tiktoken>=0.5.2
```

### 3. Create Environment File

Create a `.env` file in your project root:

```env
# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_INDEX_NAME=atara
PINECONE_HOST=https://atara-lcbpurk.svc.aped-4627-b74a.pinecone.io

# Google Gemini (for LLM responses)
GOOGLE_API_KEY=your_google_api_key_here

# App Configuration
DEBUG=false
PORT=8000
```

### 4. Test Basic Functionality

Run the basic test to verify everything works:

```bash
python test_basic.py
```

This will test:
- ✅ Package imports
- ✅ Configuration loading
- ✅ Embedding model
- ✅ Environment variables

### 5. Test Full Integration

Once basic tests pass, test the full Pinecone integration:

```bash
python test_pinecone.py
```

## 🐛 Common Issues & Solutions

### Issue: Numpy Installation Fails
**Solution**: Use numpy >= 1.26.0 for Python 3.13 compatibility

### Issue: Package Version Conflicts
**Solution**: Use `>=` instead of `==` in requirements.txt to allow compatible versions

### Issue: Import Errors
**Solution**: Install packages in the correct order (core → ML → web → docs)

### Issue: Python 3.13 Compatibility
**Solution**: Updated all packages to versions that support Python 3.13

## 📋 Verification Checklist

- [ ] All dependencies installed successfully
- [ ] `.env` file created with API keys
- [ ] `python test_basic.py` passes
- [ ] `python test_pinecone.py` passes
- [ ] `python example_usage.py` works

## 🚀 Next Steps

Once everything is working:

1. **Test the integration**: `python test_pinecone.py`
2. **Run examples**: `python example_usage.py`
3. **Start the API**: `python -m uvicorn app.main:app --reload`
4. **Ingest your documents**: Use the `/ingest` endpoint
5. **Query your data**: Use the `/query` endpoint

## 📞 Need Help?

If you're still having issues:

1. Check your Python version: `python --version`
2. Check your pip version: `pip --version`
3. Try creating a fresh virtual environment
4. Make sure you have the latest pip: `python -m pip install --upgrade pip`

## 🔍 Debug Mode

Enable debug mode in your `.env` file for more detailed error messages:

```env
DEBUG=true
``` 