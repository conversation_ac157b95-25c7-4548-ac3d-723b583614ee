#!/usr/bin/env python3
"""
Check what's actually in the .env file
"""

import os
from dotenv import load_dotenv

def check_env_file():
    """Check the .env file contents"""
    print("🔍 Checking .env file...")
    print("=" * 40)
    
    # Load environment variables
    load_dotenv()
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        return False
    
    print("📄 .env file found!")
    print("\n📋 Contents of .env file:")
    print("-" * 30)
    
    with open('.env', 'r') as f:
        content = f.read()
        print(content)
    
    print("\n🔑 Environment variables loaded:")
    print("-" * 30)
    
    # Check specific variables
    variables_to_check = [
        'PINECONE_API_KEY',
        'PINECONE_API', 
        'GOOGLE_API_KEY',
        'PINECONE_INDEX_NAME',
        'PINECONE_HOST'
    ]
    
    for var in variables_to_check:
        value = os.getenv(var)
        if value:
            # Mask the value for security
            masked = '*' * (len(value) - 4) + value[-4:] if len(value) > 4 else '***'
            print(f"✅ {var}: {masked}")
        else:
            print(f"❌ {var}: Not found")
    
    print("\n🔧 Testing Pydantic Settings...")
    print("-" * 30)
    
    try:
        import sys
        sys.path.append('app')
        from app.config import settings
        
        print("✅ Settings loaded successfully!")
        print(f"   - pinecone_api_key: {'*' * 10}{settings.pinecone_api_key[-4:] if settings.pinecone_api_key else 'None'}")
        print(f"   - pinecone_api: {'*' * 10}{settings.pinecone_api[-4:] if settings.pinecone_api else 'None'}")
        print(f"   - google_api_key: {'*' * 10}{settings.google_api_key[-4:] if settings.google_api_key else 'None'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading settings: {e}")
        return False

if __name__ == "__main__":
    check_env_file() 