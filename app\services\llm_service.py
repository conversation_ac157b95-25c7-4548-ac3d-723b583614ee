"""
LLM service for Google Gemini integration.
This module handles all LLM operations including response generation
and prompt management.
"""

import logging
from typing import List
import google.generativeai as genai

from app.config import settings
from app.models import SearchResult

logger = logging.getLogger(__name__)


class LLMService:
    """Service for handling LLM operations with Google Gemini."""

    def __init__(self):
        """Initialize the LLM service with Google Gemini."""
        self._initialize_gemini()

    def _initialize_gemini(self) -> None:
        """Initialize Google Gemini client."""
        try:
            genai.configure(api_key=settings.google_api_key)
            self.model = genai.GenerativeModel(settings.gemini_model)
            logger.info("✅ Gemini LLM initialized successfully")
        except Exception as e:
            logger.error(f"❌ Failed to initialize Gemini LLM: {e}")
            self.model = None

    def generate_response(self, query: str, context: List[SearchResult]) -> str:
        """
        Generate response using Gemini with context from search results.

        Args:
            query: User's question
            context: List of relevant search results

        Returns:
            Generated response string
        """
        try:
            if not self.model:
                logger.warning("Gemini model not available, returning mock response")
                return self._get_mock_response(query, context)

            # Build context from search results
            context_text = self._build_context_text(context)

            # Create prompt with legal assistant instructions
            prompt = self._create_prompt(query, context_text)

            # Generate response
            response = self.model.generate_content(prompt)
            return response.text

        except Exception as e:
            logger.error(f"Error generating LLM response: {e}")
            return self._get_mock_response(query, context)

    def _build_context_text(self, context: List[SearchResult]) -> str:
        """
        Build context text from search results.

        Args:
            context: List of search results

        Returns:
            Formatted context text
        """
        if not context:
            return "No relevant legal context found."

        context_parts = []
        for result in context:
            source_info = f"Source: {result.source}"
            if result.section:
                source_info += f" - {result.section}"

            context_parts.append(f"{source_info}\n{result.content}")

        return "\n\n".join(context_parts)

    def _create_prompt(self, query: str, context_text: str) -> str:
        """
        Create a well-structured prompt for the LLM.

        Args:
            query: User's question
            context_text: Formatted context from search results

        Returns:
            Complete prompt string
        """
        return f"""You are a helpful legal assistant for Pakistani law. Answer the user's question based on the provided legal context.

Context from Pakistani legal documents:
{context_text}

User Question: {query}

Instructions:
1. Answer based only on the provided context
2. Be accurate and specific
3. Mention relevant legal sections/articles
4. If you cannot answer from the context, say so
5. Keep the answer clear and understandable for non-lawyers
6. Always cite the source (e.g., "According to PPC Section X" or "Constitution Article Y")

Answer:"""

    def _get_mock_response(self, query: str, context: List[SearchResult]) -> str:
        """
        Return a mock response for testing when Gemini is not available.

        Args:
            query: User's question
            context: Search results

        Returns:
            Mock response string
        """
        context_summary = (
            "Mock legal context"
            if not context
            else f"Found {len(context)} relevant documents"
        )

        return f"""Mock Response for: "{query}"

Based on the provided legal context ({context_summary}), here is a mock response for testing purposes:

This is a placeholder response that simulates what the actual legal assistant would provide. In a real implementation, this would contain:

1. A detailed answer based on Pakistani legal documents
2. References to specific sections of the law
3. Clear explanations for non-lawyers
4. Proper citations to legal sources

For testing purposes, this mock response confirms that:
- The API endpoint is working correctly
- CORS is properly configured
- The request/response flow is functional

To get real legal assistance, please ensure that:
- Pinecone API key is configured
- Google Gemini API key is configured
- Legal documents are ingested into the vector database

Query: {query}
Context sources: {[result.source for result in context]}"""
