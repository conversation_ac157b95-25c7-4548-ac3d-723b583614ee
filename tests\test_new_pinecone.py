#!/usr/bin/env python3
"""
Test script for the updated Pinecone configuration
Tests the new API and metadata structure from document_ingester.py
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add app directory to path
sys.path.append('app')

from app.services import RAGService, VectorService
from app.config import settings

def test_pinecone_connection():
    """Test basic Pinecone connection with new API"""
    print("🔍 Testing Updated Pinecone Connection...")
    
    try:
        vector_service = VectorService()
        
        if not vector_service.index:
            print("❌ Pinecone index not initialized")
            return False
        
        # Get index statistics
        stats = vector_service.index.describe_index_stats()
        print(f"✅ Successfully connected to Pinecone index '{settings.pinecone_index_name}'")
        print(f"📊 Index Statistics:")
        print(f"   - Total vectors: {stats.total_vector_count}")
        print(f"   - Dimension: {stats.dimension}")
        print(f"   - Index fullness: {stats.index_fullness}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to connect to Pinecone: {e}")
        return False

def test_metadata_structure():
    """Test that the metadata structure matches document_ingester.py"""
    print("\n📋 Testing Metadata Structure...")
    
    try:
        # Sample document with the same metadata structure as document_ingester.py
        test_document = {
            'id': 'test_metadata_1',
            'content': 'This is a test document about Pakistani law.',
            'metadata': {
                'source': 'Test Source',
                'filename': 'test.pdf',
                'document_type': 'constitution',
                'chunk_index': 0,
                'total_chunks': 1,
                'chunk_type': 'constitutional_article',
                'content_length': 45,
                'content': 'This is a test document about Pakistani law.',
                'article_number': '1A',
                'rights_category': 'civil_liberties'
            }
        }
        
        vector_service = VectorService()
        
        if not vector_service.index:
            print("❌ Pinecone index not available for testing")
            return False
        
        # Test upsert with the metadata structure
        success = vector_service.upsert_documents([test_document])
        
        if success:
            print("✅ Successfully upserted document with correct metadata structure")
            
            # Test search to verify metadata retrieval
            results = vector_service.search("Pakistani law", top_k=1)
            
            if results:
                result = results[0]
                print(f"✅ Search result metadata:")
                print(f"   - Source: {result.source}")
                print(f"   - Section: {result.section}")
                print(f"   - Document Type: {result.document_type}")
                print(f"   - Score: {result.score}")
                return True
            else:
                print("❌ No search results found")
                return False
        else:
            print("❌ Failed to upsert test document")
            return False
            
    except Exception as e:
        print(f"❌ Error testing metadata structure: {e}")
        return False

def test_search_with_metadata():
    """Test search functionality with rich metadata"""
    print("\n🔍 Testing Search with Metadata...")
    
    try:
        rag_service = RAGService()
        
        # Test query
        query = "Pakistani law"
        results = rag_service.vector_service.search(query, top_k=3)
        
        print(f"✅ Search completed successfully")
        print(f"   - Query: '{query}'")
        print(f"   - Results found: {len(results)}")
        
        for i, result in enumerate(results[:2], 1):
            print(f"   - Result {i}:")
            print(f"     Score: {result.score:.3f}")
            print(f"     Source: {result.source}")
            print(f"     Section: {result.section}")
            print(f"     Document Type: {result.document_type}")
            print(f"     Content: {result.content[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ Error during search: {e}")
        return False

def test_rag_query():
    """Test full RAG query with LLM response"""
    print("\n🤖 Testing Full RAG Query...")
    
    try:
        rag_service = RAGService()
        
        # Test query
        query = "What are fundamental rights in Pakistan?"
        answer, search_results = rag_service.query(query)
        
        print(f"✅ RAG query completed successfully")
        print(f"   - Query: '{query}'")
        print(f"   - Search results: {len(search_results)}")
        print(f"   - Answer length: {len(answer)} characters")
        print(f"   - Answer preview: {answer[:200]}...")
        
        return True
    except Exception as e:
        print(f"❌ Error during RAG query: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Updated Pinecone Configuration Test Suite")
    print("=" * 60)
    print(f"Index Name: {settings.pinecone_index_name}")
    print(f"Host: {settings.pinecone_host}")
    print(f"Embedding Dimension: {settings.embedding_dimension}")
    print("=" * 60)
    
    tests = [
        test_pinecone_connection,
        test_metadata_structure,
        test_search_with_metadata,
        test_rag_query
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Updated Pinecone configuration is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check your configuration and try again.")
    
    return passed == total

if __name__ == "__main__":
    main() 