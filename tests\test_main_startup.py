#!/usr/bin/env python3
"""
Test script to verify main.py startup event with Pinecone initialization
"""

import asyncio
import sys
import os

# Add app directory to path
sys.path.append('app')

from app.main import app

async def test_startup_event():
    """Test the startup event"""
    print("🚀 Testing Main App Startup Event...")
    print("=" * 50)
    
    try:
        # Trigger startup event
        await app.router.startup()
        print("✅ Startup event completed successfully")
        return True
    except Exception as e:
        print(f"❌ Startup event failed: {e}")
        return False

async def test_health_endpoints():
    """Test the health endpoints"""
    print("\n🏥 Testing Health Endpoints...")
    print("=" * 30)
    
    try:
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        print(f"✅ Root endpoint: {response.status_code}")
        
        # Test Pinecone health endpoint
        response = client.get("/health/pinecone")
        print(f"✅ Pinecone health endpoint: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   - Status: {data.get('status')}")
            if data.get('pinecone', {}).get('connected'):
                print(f"   - Index: {data['pinecone']['index']}")
                print(f"   - Vectors: {data['pinecone']['total_vectors']}")
                print(f"   - Dimension: {data['pinecone']['dimension']}")
        
        return True
    except Exception as e:
        print(f"❌ Health endpoints test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Main App Integration Test")
    print("=" * 50)
    
    tests = [
        test_startup_event,
        test_health_endpoints
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if await test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Main app is working correctly with Pinecone.")
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main()) 