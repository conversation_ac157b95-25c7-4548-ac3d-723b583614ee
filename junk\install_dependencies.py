#!/usr/bin/env python3
"""
Install dependencies step by step to avoid conflicts
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False

def main():
    print("🚀 Installing Dependencies for Pinecone Integration")
    print("=" * 60)
    
    # Check Python version
    python_version = sys.version_info
    print(f"🐍 Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major == 3 and python_version.minor >= 13:
        print("⚠️  Detected Python 3.13+ - Using compatible package versions")
    
    # Step 1: Upgrade pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip"):
        print("⚠️  Pip upgrade failed, continuing anyway...")
    
    # Step 2: Install core dependencies first
    core_packages = [
        "numpy>=1.26.0",
        "pydantic>=2.5.0",
        "pydantic-settings>=2.1.0",
        "python-dotenv>=1.0.0"
    ]
    
    for package in core_packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}"):
            print(f"❌ Failed to install {package}")
            return False
    
    # Step 3: Install ML/AI dependencies
    ml_packages = [
        "sentence-transformers>=2.2.2",
        "google-generativeai>=0.3.2",
        "pinecone-client>=2.2.4"
    ]
    
    for package in ml_packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}"):
            print(f"❌ Failed to install {package}")
            return False
    
    # Step 4: Install web framework
    web_packages = [
        "fastapi>=0.104.1",
        "uvicorn>=0.24.0",
        "httpx>=0.25.2"
    ]
    
    for package in web_packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}"):
            print(f"❌ Failed to install {package}")
            return False
    
    # Step 5: Install document processing
    doc_packages = [
        "PyPDF2>=3.0.1",
        "python-docx>=1.1.0",
        "tiktoken>=0.5.2"
    ]
    
    for package in doc_packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"Installing {package}"):
            print(f"❌ Failed to install {package}")
            return False
    
    print("\n" + "=" * 60)
    print("🎉 All dependencies installed successfully!")
    print("\nNext steps:")
    print("1. Create a .env file with your API keys")
    print("2. Run: python test_pinecone.py")
    print("3. Run: python example_usage.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 