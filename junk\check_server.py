#!/usr/bin/env python3
"""
Simple script to check if the FastAPI server is working
"""
import requests
import time
import sys

def check_server():
    """Check if the server is responding"""
    print("🔍 Checking FastAPI server status...")
    
    # Test basic connectivity
    try:
        print("Testing basic connectivity...")
        response = requests.get('http://localhost:8000/', timeout=10)
        print(f"✅ Root endpoint: Status {response.status_code}")
        print(f"Response: {response.json()}")
    except requests.exceptions.Timeout:
        print("❌ Timeout connecting to root endpoint")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - server might not be running")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Test health endpoint (correct path)
    try:
        print("\nTesting health endpoint...")
        response = requests.get('http://localhost:8000/api/v1/health', timeout=10)
        print(f"✅ Health endpoint: Status {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False
    
    # Test debug endpoint
    try:
        print("\nTesting debug endpoint...")
        response = requests.get('http://localhost:8000/debug/cors', timeout=10)
        print(f"✅ Debug endpoint: Status {response.status_code}")
        print(f"Response: {response.json()}")
    except Exception as e:
        print(f"❌ Debug endpoint error: {e}")
        return False
    
    print("\n🎉 Server appears to be working correctly!")
    return True

if __name__ == "__main__":
    check_server() 