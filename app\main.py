"""
Main FastAPI application for the Legal RAG API.

This module contains the main FastAPI application setup, middleware configuration,
and startup/shutdown event handlers.
"""

import logging
import time
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware

from app.config import settings
from app.routes import api_router, admin_router
from app.services import RAGService

# ============================================================================
# Logging Configuration
# ============================================================================

logging.basicConfig(
    level=logging.INFO if not settings.debug else logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)

# ============================================================================
# Application Lifecycle Management
# ============================================================================

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application startup and shutdown events."""
    # Startup
    await startup_event()
    yield
    # Shutdown
    await shutdown_event()


async def startup_event():
    """Initialize services on application startup."""
    logger.info("🚀 Starting Legal RAG API...")
    
    try:
        # Test Pinecone connection
        rag_service = RAGService()
        stats = rag_service.get_index_stats()
        
        logger.info(f"✅ Pinecone connected successfully")
        logger.info(f"   - Index: {settings.pinecone_index_name}")
        logger.info(f"   - Total vectors: {stats.get('total_vector_count', 0)}")
        logger.info(f"   - Dimension: {stats.get('dimension', 384)}")
        
        # Test embedding model
        test_embedding = rag_service.vector_service.create_embedding("test")
        logger.info(f"✅ Embedding model loaded successfully")
        logger.info(f"   - Embedding dimension: {len(test_embedding)}")
        
        # Test LLM service
        if rag_service.llm_service.model:
            logger.info(f"✅ LLM service initialized successfully")
            logger.info(f"   - Model: {settings.gemini_model}")
        else:
            logger.warning("⚠️  LLM service not available")
        
        logger.info("🎉 All services initialized successfully!")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize services: {e}")
        logger.warning("⚠️  Application starting with limited functionality")


async def shutdown_event():
    """Cleanup on application shutdown."""
    logger.info("🛑 Shutting down Legal RAG API...")


# ============================================================================
# FastAPI Application Setup
# ============================================================================

app = FastAPI(
    title="Legal RAG API",
    description="Pakistani Legal Document Retrieval-Augmented Generation System",
    version="1.0.0",
    debug=settings.debug,
    lifespan=lifespan
)

# ============================================================================
# Middleware Configuration
# ============================================================================

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.middleware("http")
async def debug_middleware(request: Request, call_next):
    """
    Debug middleware to log all requests and CORS headers.
    
    This middleware provides detailed logging for debugging purposes,
    including request details, headers, and processing time.
    """
    start_time = time.time()
    
    # Log request details
    logger.info(f"=== INCOMING REQUEST ===")
    logger.info(f"Method: {request.method}")
    logger.info(f"URL: {request.url}")
    logger.info(f"Client: {request.client.host if request.client else 'Unknown'}")
    
    # Log all headers
    logger.info("Headers:")
    for name, value in request.headers.items():
        logger.info(f"  {name}: {value}")
    
    # Log CORS-specific headers
    origin = request.headers.get("origin")
    logger.info(f"Origin: {origin}")
    logger.info(f"Allowed origins: {settings.cors_origins}")
    
    if origin:
        if origin in settings.cors_origins:
            logger.info("✅ Origin is in allowed list")
        else:
            logger.warning(f"❌ Origin {origin} is NOT in allowed list!")
    
    # Process request
    try:
        response = await call_next(request)
        
        # Log response details
        process_time = time.time() - start_time
        logger.info(f"Response Status: {response.status_code}")
        logger.info(f"Process Time: {process_time:.4f}s")
        
        # Log CORS response headers
        cors_headers = {
            "Access-Control-Allow-Origin": response.headers.get("access-control-allow-origin"),
            "Access-Control-Allow-Methods": response.headers.get("access-control-allow-methods"),
            "Access-Control-Allow-Headers": response.headers.get("access-control-allow-headers"),
            "Access-Control-Allow-Credentials": response.headers.get("access-control-allow-credentials"),
        }
        
        logger.info("CORS Response Headers:")
        for name, value in cors_headers.items():
            logger.info(f"  {name}: {value}")
        
        logger.info("=== END REQUEST ===\n")
        
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(f"❌ Request failed after {process_time:.4f}s: {e}")
        logger.info("=== END REQUEST (ERROR) ===\n")
        raise

# ============================================================================
# Router Registration
# ============================================================================

app.include_router(api_router, prefix="/api/v1")
app.include_router(admin_router, prefix="/api/v1/admin", tags=["admin"])

# ============================================================================
# Root Endpoints
# ============================================================================

@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "message": "Legal RAG API", 
        "version": "1.0.0",
        "docs": "/docs",
        "cors_origins": settings.cors_origins,
        "debug": settings.debug
    }


@app.get("/health/pinecone")
async def pinecone_health():
    """
    Health check endpoint specifically for Pinecone.
    
    Returns detailed status of Pinecone connection, embedding model,
    and LLM service.
    """
    try:
        rag_service = RAGService()
        stats = rag_service.get_index_stats()
        
        return {
            "status": "healthy",
            "pinecone": {
                "connected": True,
                "index": settings.pinecone_index_name,
                "total_vectors": stats.get('total_vector_count', 0),
                "dimension": stats.get('dimension', 384),
                "index_fullness": stats.get('index_fullness', 0.0)
            },
            "embedding_model": {
                "loaded": rag_service.vector_service.embedding_model is not None,
                "dimension": len(rag_service.vector_service.create_embedding("test"))
            },
            "llm_service": {
                "available": rag_service.llm_service.model is not None,
                "model": settings.gemini_model
            }
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "pinecone": {
                "connected": False
            }
        }


@app.get("/debug/cors")
async def debug_cors():
    """Debug endpoint to check CORS configuration."""
    return {
        "cors_origins": settings.cors_origins,
        "debug_mode": settings.debug,
        "port": settings.port,
        "message": "CORS configuration debug info"
    }

# ============================================================================
# Application Entry Point
# ============================================================================

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=settings.port,
        reload=settings.debug
    )