#!/usr/bin/env python3
"""
Test script to check environment configuration
"""
from app.config import settings

def test_environment():
    """Test if environment variables are properly configured"""
    print("🔍 Testing Environment Configuration...\n")
    
    print("=== API Keys ===")
    print(f"Pinecone API Key: {'✅ Set' if settings.pinecone_api_key else '❌ Not Set'}")
    print(f"Google API Key: {'✅ Set' if settings.google_api_key else '❌ Not Set'}")
    
    print("\n=== Pinecone Configuration ===")
    print(f"Index Name: {settings.pinecone_index_name}")
    print(f"Host: {settings.pinecone_host}")
    
    print("\n=== App Configuration ===")
    print(f"Debug Mode: {settings.debug}")
    print(f"Port: {settings.port}")
    print(f"CORS Origins: {len(settings.cors_origins)} origins configured")
    
    print("\n=== RAG Settings ===")
    print(f"Chunk Size: {settings.chunk_size}")
    print(f"Chunk Overlap: {settings.chunk_overlap}")
    print(f"Max Results: {settings.max_results}")
    print(f"Embedding Dimension: {settings.embedding_dimension}")
    
    # Test if we can import the services
    print("\n=== Service Import Test ===")
    try:
        from app.services import RAGService
        print("✅ RAGService can be imported")
        
        # Try to create an instance (this will fail if API keys are missing)
        try:
            rag = RAGService()
            print("✅ RAGService can be instantiated")
        except Exception as e:
            print(f"❌ RAGService instantiation failed: {e}")
            
    except Exception as e:
        print(f"❌ RAGService import failed: {e}")

if __name__ == "__main__":
    test_environment() 