#!/usr/bin/env python3
"""
Test script for FastAPI chatbot endpoints
"""

import requests
import json
import time
from typing import Dict, Any

# API Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_health_check():
    """Test health check endpoint"""
    print("🏥 Testing Health Check...")
    
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_chat_endpoint():
    """Test main chat endpoint"""
    print("\n💬 Testing Chat Endpoint...")
    
    # Test data
    test_queries = [
        "What is the 18th amendment?",
        "What are fundamental rights in Pakistan?",
        "How is murder defined in Pakistani law?"
    ]
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        
        try:
            payload = {
                "query": query,
                "top_k": 3
            }
            
            response = requests.post(
                f"{API_BASE}/chat",
                json=payload,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Chat response received")
                print(f"   - Answer length: {len(data.get('answer', ''))}")
                print(f"   - Sources found: {len(data.get('sources', []))}")
                print(f"   - Processing time: {data.get('processing_time', 'N/A')}s")
                print(f"   - Conversation ID: {data.get('conversation_id', 'N/A')}")
            else:
                print(f"❌ Chat request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Chat request error: {e}")
            return False
    
    return True

def test_search_endpoint():
    """Test search endpoint"""
    print("\n🔍 Testing Search Endpoint...")
    
    try:
        query = "18th amendment"
        response = requests.get(f"{API_BASE}/search?query={query}&top_k=3")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Search response received")
            print(f"   - Query: {data.get('query')}")
            print(f"   - Results found: {data.get('total_results')}")
            return True
        else:
            print(f"❌ Search request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Search request error: {e}")
        return False

def test_admin_stats():
    """Test admin stats endpoint"""
    print("\n📊 Testing Admin Stats...")
    
    try:
        response = requests.get(f"{API_BASE}/admin/stats")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Stats response received")
            print(f"   - Total vectors: {data.get('total_vectors')}")
            print(f"   - Index dimension: {data.get('index_dimension')}")
            print(f"   - Index fullness: {data.get('index_fullness')}")
            return True
        else:
            print(f"❌ Stats request failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Stats request error: {e}")
        return False

def test_document_ingestion():
    """Test document ingestion endpoint"""
    print("\n📄 Testing Document Ingestion...")
    
    # Sample documents for testing
    test_documents = [
        {
            "id": "test_doc_1",
            "content": "The 18th Amendment to the Constitution of Pakistan was passed in 2010. It devolved several federal ministries to the provinces.",
            "metadata": {
                "source": "Constitution of Pakistan",
                "section": "18th Amendment",
                "document_type": "constitution"
            }
        },
        {
            "id": "test_doc_2",
            "content": "Fundamental rights are guaranteed under Articles 8 to 28 of the Constitution of Pakistan.",
            "metadata": {
                "source": "Constitution of Pakistan",
                "section": "Fundamental Rights",
                "document_type": "constitution"
            }
        }
    ]
    
    try:
        payload = {
            "documents": test_documents
        }
        
        response = requests.post(
            f"{API_BASE}/admin/ingest/documents",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Document ingestion successful")
            print(f"   - Message: {data.get('message')}")
            print(f"   - Documents processed: {data.get('documents_processed')}")
            return True
        else:
            print(f"❌ Document ingestion failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Document ingestion error: {e}")
        return False

def test_error_handling():
    """Test error handling"""
    print("\n⚠️  Testing Error Handling...")
    
    # Test empty query
    try:
        payload = {"query": ""}
        response = requests.post(f"{API_BASE}/chat", json=payload)
        
        if response.status_code == 400:
            print("✅ Empty query properly rejected")
        else:
            print(f"❌ Empty query not properly handled: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False
    
    return True

def main():
    """Run all API tests"""
    print("🚀 FastAPI Chatbot Test Suite")
    print("=" * 50)
    
    tests = [
        test_health_check,
        test_chat_endpoint,
        test_search_endpoint,
        test_admin_stats,
        test_document_ingestion,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All API tests passed! Your chatbot is ready.")
        print("\nYou can now:")
        print("1. Start your React frontend")
        print("2. Connect to the API at: http://localhost:8000")
        print("3. Use the /api/v1/chat endpoint for queries")
    else:
        print("⚠️  Some tests failed. Please check your API setup.")
    
    return passed == total

if __name__ == "__main__":
    main() 