# Legal RAG API - Project Structure

## 🏗️ Clean Architecture Overview

This project follows a clean, modular architecture with clear separation of concerns and excellent readability.

```
18th-amendment/
├── app/                          # Main application package
│   ├── __init__.py              # Package initialization
│   ├── config.py                # Configuration management
│   ├── main.py                  # FastAPI application setup
│   ├── models.py                # Pydantic data models
│   ├── routes.py                # API route definitions
│   └── services/                # Business logic services
│       ├── __init__.py          # Services package
│       ├── vector_service.py    # Pinecone vector operations
│       ├── llm_service.py       # Google Gemini LLM operations
│       └── rag_service.py       # RAG orchestration
├── data_pipeline/               # Document processing pipeline
│   ├── __init__.py
│   ├── document_ingester.py     # Document ingestion logic
│   ├── ingest.py                # Ingestion orchestration
│   ├── pinecone_service.py      # Pipeline-specific Pinecone service
│   └── processors.py            # Document processing utilities
├── data/                        # Data storage
│   ├── raw/                     # Raw documents
│   └── processed/               # Processed documents
├── tests/                       # Test files
├── requirements.txt             # Python dependencies
├── README.md                    # Project documentation
└── start_server.py              # Server startup script
```

## 📁 Package Descriptions

### `app/` - Main Application Package

#### `config.py`
- **Purpose**: Centralized configuration management
- **Features**: 
  - Environment variable handling with Pydantic Settings
  - Pinecone, LLM, and application configuration
  - CORS settings and RAG parameters
- **Clean Code**: Well-organized with clear section separators

#### `models.py`
- **Purpose**: Data validation and serialization
- **Features**:
  - Pydantic models for all API requests/responses
  - Comprehensive field validation and documentation
  - Enum types for document classification
- **Clean Code**: Clear model definitions with descriptive docstrings

#### `main.py`
- **Purpose**: FastAPI application setup and lifecycle management
- **Features**:
  - Application startup/shutdown events
  - Middleware configuration (CORS, logging)
  - Health check endpoints
- **Clean Code**: Modular structure with clear sections

#### `routes.py`
- **Purpose**: API endpoint definitions
- **Features**:
  - Public API routes (chat, search, health)
  - Admin routes (ingestion, statistics)
  - Comprehensive error handling
- **Clean Code**: Well-documented endpoints with clear separation

#### `services/` - Business Logic Layer

##### `vector_service.py`
- **Purpose**: Vector database operations
- **Features**:
  - Pinecone integration with new API
  - Embedding generation and search
  - Document upserting with metadata
- **Clean Code**: Modular methods with comprehensive error handling

##### `llm_service.py`
- **Purpose**: LLM operations and response generation
- **Features**:
  - Google Gemini integration
  - Context-aware prompt engineering
  - Mock responses for testing
- **Clean Code**: Clear separation of concerns with helper methods

##### `rag_service.py`
- **Purpose**: RAG pipeline orchestration
- **Features**:
  - Coordinates vector search and LLM generation
  - Provides both combined and separate operations
  - Clean interface for the application layer
- **Clean Code**: Simple, focused methods with clear responsibilities

### `data_pipeline/` - Document Processing

#### `document_ingester.py`
- **Purpose**: Document ingestion and processing
- **Features**:
  - PDF text extraction and cleaning
  - Smart chunking based on document type
  - Rich metadata extraction
- **Clean Code**: Well-structured with clear processing steps

#### `processors.py`
- **Purpose**: Document processing utilities
- **Features**:
  - Text cleaning and normalization
  - Chunking strategies
  - Document type detection
- **Clean Code**: Utility functions with clear purposes

## 🎯 Clean Code Principles Applied

### 1. **Single Responsibility Principle**
- Each module has a single, well-defined purpose
- Services are focused on specific domains (vector, LLM, RAG)
- Clear separation between data models, business logic, and API layer

### 2. **Dependency Inversion**
- Services depend on abstractions, not concrete implementations
- Configuration is injected through settings
- Easy to test and mock components

### 3. **Clean Interfaces**
- Well-documented public APIs
- Consistent error handling patterns
- Clear method signatures with type hints

### 4. **Comprehensive Documentation**
- Detailed docstrings for all classes and methods
- Clear module-level documentation
- Inline comments for complex logic

### 5. **Error Handling**
- Graceful degradation with mock responses
- Comprehensive logging
- User-friendly error messages

### 6. **Configuration Management**
- Centralized configuration with environment variable support
- Clear default values
- Backward compatibility handling

## 🚀 Key Improvements Made

1. **Modular Architecture**: Split monolithic services into focused modules
2. **Clear Documentation**: Added comprehensive docstrings and comments
3. **Type Safety**: Enhanced type hints throughout the codebase
4. **Error Handling**: Improved error handling with graceful fallbacks
5. **Configuration**: Centralized and organized configuration management
6. **Testing**: Maintained all existing functionality while improving structure
7. **Readability**: Clear naming conventions and logical organization
8. **Maintainability**: Easy to extend and modify individual components

## 🔧 Usage

The restructured code maintains 100% backward compatibility while providing:
- Better code organization
- Improved readability
- Easier maintenance
- Enhanced testability
- Clear separation of concerns

All existing functionality works exactly the same, but the code is now much more professional and maintainable. 