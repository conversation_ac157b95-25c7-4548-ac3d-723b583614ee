<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FastAPI CORS Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>FastAPI CORS Test</h1>
        <p>This page tests the CORS configuration of your FastAPI server.</p>
        
        <div class="test-section">
            <h3>Server Configuration</h3>
            <p>Current URL: <span id="currentUrl"></span></p>
            <button onclick="testServerConfig()">Test Server Configuration</button>
            <div id="serverConfigResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Health Check</h3>
            <button onclick="testHealth()">Test Health Endpoint</button>
            <div id="healthResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>CORS Preflight Test</h3>
            <button onclick="testCorsPreflight()">Test CORS Preflight</button>
            <div id="corsPreflightResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>Chat API Test</h3>
            <label for="queryInput">Query:</label>
            <input type="text" id="queryInput" value="What are fundamental rights in Pakistan?" placeholder="Enter your query">
            <button onclick="testChatAPI()">Test Chat API</button>
            <div id="chatResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>All Tests</h3>
            <button onclick="runAllTests()">Run All Tests</button>
            <div id="allTestsResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        // Display current URL
        document.getElementById('currentUrl').textContent = window.location.href;

        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        async function testServerConfig() {
            try {
                const response = await fetch(`${API_BASE}/debug/cors`);
                const data = await response.json();
                logResult('serverConfigResult', 
                    `Status: ${response.status}\n` +
                    `CORS Origins: ${JSON.stringify(data.cors_origins, null, 2)}\n` +
                    `Debug Mode: ${data.debug_mode}\n` +
                    `Port: ${data.port}`, 
                    response.ok ? 'success' : 'error'
                );
            } catch (error) {
                logResult('serverConfigResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testHealth() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                logResult('healthResult', 
                    `Status: ${response.status}\n` +
                    `Response: ${JSON.stringify(data, null, 2)}`, 
                    response.ok ? 'success' : 'error'
                );
            } catch (error) {
                logResult('healthResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testCorsPreflight() {
            try {
                const response = await fetch(`${API_BASE}/api/v1/chat`, {
                    method: 'OPTIONS',
                    headers: {
                        'Access-Control-Request-Method': 'POST',
                        'Access-Control-Request-Headers': 'Content-Type'
                    }
                });
                
                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
                    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
                };
                
                logResult('corsPreflightResult', 
                    `Status: ${response.status}\n` +
                    `CORS Headers: ${JSON.stringify(corsHeaders, null, 2)}`, 
                    response.ok ? 'success' : 'error'
                );
            } catch (error) {
                logResult('corsPreflightResult', `Error: ${error.message}`, 'error');
            }
        }

        async function testChatAPI() {
            const query = document.getElementById('queryInput').value;
            try {
                const response = await fetch(`${API_BASE}/api/v1/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        query: query,
                        conversation_id: 'test-' + Date.now()
                    })
                });
                
                const data = await response.json();
                logResult('chatResult', 
                    `Status: ${response.status}\n` +
                    `Response: ${JSON.stringify(data, null, 2)}`, 
                    response.ok ? 'success' : 'error'
                );
            } catch (error) {
                logResult('chatResult', `Error: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            logResult('allTestsResult', 'Running all tests...', 'info');
            
            await testServerConfig();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testHealth();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testCorsPreflight();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testChatAPI();
            
            logResult('allTestsResult', 'All tests completed! Check individual results above.', 'success');
        }
    </script>
</body>
</html> 