import os
import uuid
from typing import Dict
from processors import DocumentProcessor
from pinecone_service import PineconeService


class DocumentIngester:
    """Simple document ingester for legal documents"""
    
    def __init__(self):
        self.processor = DocumentProcessor()
        self.pinecone = PineconeService()
    
    def ingest_pdf(self, file_path: str, source_name: str, doc_type: str) -> bool:
        """Ingest a PDF file into the vector database with smart chunking"""
        try:
            print(f"\nProcessing: {file_path}")
            
            # Extract text
            text = self.processor.extract_text_from_pdf(file_path)
            print(f"Extracted {len(text)} characters")
            
            # Clean text
            text = self.processor.clean_text(text)
            
            # Create smart chunks based on document type
            chunks = self.processor.chunk_text(text, doc_type)
            print(f"Created {len(chunks)} chunks using smart chunking")
            
            # Prepare documents for upload
            documents = []
            filename = os.path.basename(file_path)
            
            for i, chunk in enumerate(chunks):
                # Determine chunk type
                chunk_type = self._identify_chunk_type(chunk, doc_type)
                
                doc = {
                    'id': str(uuid.uuid4()),
                    'content': chunk,
                    'metadata': {
                        'source': source_name,
                        'filename': filename,
                        'document_type': doc_type,
                        'chunk_index': i,
                        'total_chunks': len(chunks),
                        'chunk_type': chunk_type,
                        'content_length': len(chunk),
                        'content': chunk  # Store content in metadata for retrieval
                    }
                }
                
                # Add specific metadata based on document type
                if doc_type == "penal_code":
                    section_info = self._extract_section_info(chunk)
                    if section_info:
                        doc['metadata'].update(section_info)
                elif doc_type == "constitution":
                    article_info = self._extract_article_info(chunk)
                    if article_info:
                        doc['metadata'].update(article_info)
                
                documents.append(doc)
            
            # Upload to Pinecone
            success = self.pinecone.upsert_documents(documents)
            
            if success:
                print(f"✅ Successfully ingested {len(documents)} chunks")
                return True
            else:
                print("❌ Failed to ingest")
                return False
                
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    def _identify_chunk_type(self, chunk: str, doc_type: str) -> str:
        """Identify what type of content this chunk contains"""
        import re
        
        if doc_type == "penal_code":
            if re.search(r'Section\s+\d+|^\d+\.', chunk):
                return "legal_section"
            elif re.search(r'Chapter|CHAPTER', chunk, re.IGNORECASE):
                return "chapter_header"
        elif doc_type == "constitution":
            if re.search(r'Article\s+\d+|^\d+\.', chunk):
                return "constitutional_article"
            elif re.search(r'Part|PART|Chapter|CHAPTER', chunk, re.IGNORECASE):
                return "part_header"
        
        return "general_content"
    
    def _extract_section_info(self, chunk: str) -> Dict[str, str]:
        """Extract section number and title from PPC content"""
        import re
        info = {}
        
        # Look for section number
        section_match = re.search(r'(?:Section\s+)?(\d+[A-Z]*)', chunk, re.IGNORECASE)
        if section_match:
            info['section_number'] = section_match.group(1)
        
        # Look for offense type keywords
        offense_keywords = {
            'murder': 'homicide',
            'theft': 'property_crime',
            'assault': 'violence',
            'fraud': 'financial_crime',
            'defamation': 'reputation_crime'
        }
        
        for keyword, category in offense_keywords.items():
            if re.search(keyword, chunk, re.IGNORECASE):
                info['offense_category'] = category
                break
        
        return info
    
    def _extract_article_info(self, chunk: str) -> Dict[str, str]:
        """Extract article number and rights category from constitutional content"""
        import re
        info = {}
        
        # Look for article number
        article_match = re.search(r'(?:Article\s+)?(\d+[A-Z]*)', chunk, re.IGNORECASE)
        if article_match:
            info['article_number'] = article_match.group(1)
        
        # Look for rights categories
        rights_keywords = {
            'freedom': 'civil_liberties',
            'equality': 'equal_rights',
            'property': 'property_rights',
            'religion': 'religious_freedom',
            'expression': 'free_speech'
        }
        
        for keyword, category in rights_keywords.items():
            if re.search(keyword, chunk, re.IGNORECASE):
                info['rights_category'] = category
                break
        
        return info
