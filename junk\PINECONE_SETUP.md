# Pinecone Integration Setup Guide

This guide explains how to set up and use Pinecone for storing and querying vector embeddings in your 18th Amendment project.

## Configuration

Your Pinecone index is configured with the following settings:

- **Index Name**: `atara`
- **Host**: `https://atara-lcbpurk.svc.aped-4627-b74a.pinecone.io`
- **Modality**: Text
- **Vector Type**: Dense
- **Max Input**: 2,048 tokens
- **Dimension**: 384
- **Metric**: Cosine
- **Field Map**: text

## Environment Setup

1. Create a `.env` file in your project root with the following variables:

```env
# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_INDEX_NAME=atara
PINECONE_HOST=https://atara-lcbpurk.svc.aped-4627-b74a.pinecone.io

# Google Gemini (for LLM responses)
GOOGLE_API_KEY=your_google_api_key_here
```

2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Key Features

### 1. Vector Storage
- **Embedding Model**: Uses `sentence-transformers/all-MiniLM-L6-v2` which produces 384-dimensional embeddings
- **Token Limit**: Automatically truncates text to stay within the 2,048 token limit
- **Metadata**: Stores document content in the `text` field as required by your index configuration

### 2. Vector Search
- **Similarity Metric**: Uses cosine similarity for finding relevant documents
- **Top-K Results**: Configurable number of results returned
- **Metadata Retrieval**: Includes source, section, and document type information

### 3. RAG Integration
- **Document Retrieval**: Finds relevant legal documents based on user queries
- **LLM Generation**: Uses Google Gemini to generate contextual responses
- **Source Attribution**: Provides citations to legal sources

## Usage Examples

### Basic Document Ingestion

```python
from app.services import RAGService

rag_service = RAGService()

documents = [
    {
        'id': 'unique_document_id',
        'content': 'Your document content here...',
        'metadata': {
            'source': 'Constitution of Pakistan',
            'section': 'Article 8',
            'document_type': 'constitutional'
        }
    }
]

success = rag_service.ingest_documents(documents)
```

### Search for Relevant Documents

```python
# Search for documents
search_results = rag_service.vector_service.search(
    query="What is the 18th amendment?",
    top_k=5
)

for result in search_results:
    print(f"Score: {result.score}")
    print(f"Source: {result.source}")
    print(f"Content: {result.content}")
```

### RAG Query with LLM Response

```python
# Get complete RAG response
answer, sources = rag_service.query("What are fundamental rights in Pakistan?")

print(f"Answer: {answer}")
print(f"Sources: {len(sources)} documents used")
```

## Testing

Run the test suite to verify your Pinecone integration:

```bash
python test_pinecone.py
```

This will test:
- ✅ Pinecone connection
- ✅ Embedding creation
- ✅ Document ingestion
- ✅ Search functionality

## Example Usage

Run the example script to see the integration in action:

```bash
python example_usage.py
```

## API Endpoints

The integration is available through FastAPI endpoints in `app/routes.py`:

- `POST /ingest` - Ingest documents into Pinecone
- `POST /query` - Query the RAG system
- `GET /search` - Search for documents
- `GET /stats` - Get index statistics

## Important Notes

1. **Token Limits**: The system automatically handles the 2,048 token limit for your index
2. **Embedding Dimensions**: Ensures 384-dimensional embeddings match your index configuration
3. **Metadata Structure**: Uses the `text` field as required by your index field map
4. **Error Handling**: Comprehensive error handling and logging for debugging

## Troubleshooting

### Common Issues

1. **Connection Errors**: Verify your Pinecone API key and host URL
2. **Dimension Mismatch**: Ensure the embedding model produces 384-dimensional vectors
3. **Token Limit Exceeded**: The system automatically truncates long texts
4. **Empty Results**: Check if documents have been properly ingested

### Debug Mode

Enable debug mode in your `.env` file:

```env
DEBUG=true
```

This will provide more detailed logging information.

## Performance Considerations

- **Batch Processing**: For large document sets, consider processing in batches
- **Index Limits**: Your index has a 5M token starter limit
- **Query Optimization**: Use specific queries for better results
- **Caching**: Consider implementing result caching for frequently asked questions

## Security

- Keep your API keys secure and never commit them to version control
- Use environment variables for sensitive configuration
- Consider implementing rate limiting for production use 