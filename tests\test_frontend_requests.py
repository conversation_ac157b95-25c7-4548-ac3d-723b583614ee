#!/usr/bin/env python3
"""
Test script to simulate frontend requests and debug CORS issues
"""
import requests
import json
import time

def test_frontend_requests():
    """Simulate frontend requests from different origins"""
    base_url = "http://localhost:8000"
    
    # Test origins that might be used by frontend
    test_origins = [
        "http://localhost:3000",
        "http://localhost:5173", 
        "http://127.0.0.1:3000",
        "http://localhost:8080",
        "http://localhost:4000",
        "http://localhost:5000",
    ]
    
    print("=== Frontend Request Simulation ===\n")
    
    # First check if server is running
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"✅ Server is running at {base_url}")
    except:
        print(f"❌ Server is not running at {base_url}")
        print("Please start the server first: python start_server.py")
        return
    
    # Test each origin
    for origin in test_origins:
        print(f"\n--- Testing Origin: {origin} ---")
        
        # Test 1: Simple GET request
        try:
            headers = {"Origin": origin}
            response = requests.get(f"{base_url}/", headers=headers, timeout=5)
            print(f"GET / - Status: {response.status_code}")
            print(f"  CORS Origin: {response.headers.get('Access-Control-Allow-Origin', 'Not set')}")
        except Exception as e:
            print(f"GET / - Error: {e}")
        
        # Test 2: Health check
        try:
            headers = {"Origin": origin}
            response = requests.get(f"{base_url}/health", headers=headers, timeout=5)
            print(f"GET /health - Status: {response.status_code}")
            print(f"  CORS Origin: {response.headers.get('Access-Control-Allow-Origin', 'Not set')}")
        except Exception as e:
            print(f"GET /health - Error: {e}")
        
        # Test 3: CORS preflight for chat endpoint
        try:
            headers = {
                "Origin": origin,
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
            response = requests.options(f"{base_url}/api/v1/chat", headers=headers, timeout=5)
            print(f"OPTIONS /api/v1/chat - Status: {response.status_code}")
            print(f"  CORS Origin: {response.headers.get('Access-Control-Allow-Origin', 'Not set')}")
            print(f"  CORS Methods: {response.headers.get('Access-Control-Allow-Methods', 'Not set')}")
        except Exception as e:
            print(f"OPTIONS /api/v1/chat - Error: {e}")
        
        # Test 4: Actual POST request to chat
        try:
            headers = {
                "Origin": origin,
                "Content-Type": "application/json"
            }
            data = {
                "query": "test query from frontend",
                "conversation_id": f"test-{int(time.time())}"
            }
            response = requests.post(f"{base_url}/api/v1/chat", headers=headers, json=data, timeout=10)
            print(f"POST /api/v1/chat - Status: {response.status_code}")
            print(f"  CORS Origin: {response.headers.get('Access-Control-Allow-Origin', 'Not set')}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"  Response: {result.get('answer', 'No answer')[:100]}...")
            elif response.status_code == 400:
                result = response.json()
                print(f"  Error: {result.get('message', 'Unknown error')}")
        except Exception as e:
            print(f"POST /api/v1/chat - Error: {e}")

if __name__ == "__main__":
    test_frontend_requests() 