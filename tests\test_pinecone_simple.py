#!/usr/bin/env python3
"""
Simple test to check Pinecone import and API
"""
import sys

print("=== Testing Pinecone Import ===")

try:
    import pinecone
    print(f"✅ Pinecone imported successfully")
    print(f"Pinecone module: {pinecone}")
    print(f"Pinecone attributes: {dir(pinecone)}")
except Exception as e:
    print(f"❌ Failed to import pinecone: {e}")

print("\n=== Testing Pinecone Client Import ===")

try:
    import pinecone_client
    print(f"✅ Pinecone client imported successfully")
    print(f"Pinecone client module: {pinecone_client}")
    print(f"Pinecone client attributes: {dir(pinecone_client)}")
except Exception as e:
    print(f"❌ Failed to import pinecone_client: {e}")

print("\n=== Testing Direct Import ===")

try:
    from pinecone import init, Index
    print(f"✅ Direct imports successful")
    print(f"init function: {init}")
    print(f"Index class: {Index}")
except Exception as e:
    print(f"❌ Failed direct imports: {e}")

print("\n=== Package Info ===")
import subprocess
try:
    result = subprocess.run([sys.executable, '-m', 'pip', 'list'], capture_output=True, text=True)
    pinecone_lines = [line for line in result.stdout.split('\n') if 'pinecone' in line.lower()]
    print("Pinecone packages installed:")
    for line in pinecone_lines:
        print(f"  {line}")
except Exception as e:
    print(f"❌ Failed to get package info: {e}") 