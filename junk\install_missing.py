#!/usr/bin/env python3
"""
Quick script to install missing google-generativeai package
"""

import subprocess
import sys

def install_package(package):
    """Install a single package"""
    print(f"🔄 Installing {package}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    print("🔧 Installing Missing Package")
    print("=" * 40)
    
    # Install the missing package
    success = install_package("google-generativeai")
    
    if success:
        print("\n✅ Installation complete!")
        print("You can now run:")
        print("  python test_basic.py")
        print("  python example_usage.py")
    else:
        print("\n❌ Installation failed!")
        print("Try running manually:")
        print("  pip install google-generativeai")

if __name__ == "__main__":
    main() 