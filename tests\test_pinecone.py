#!/usr/bin/env python3
"""
Test script for Pinecone integration with the 'atara' index
Configured for llama-text-embed-v2 with 384 dimensions
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add app directory to path
sys.path.append('app')

from app.services import RAGService
from app.config import settings

def test_pinecone_connection():
    """Test basic Pinecone connection and index access"""
    print("🔍 Testing Pinecone Connection...")
    
    try:
        rag_service = RAGService()
        
        # Get index statistics
        stats = rag_service.get_index_stats()
        print(f"✅ Successfully connected to Pinecone index '{settings.pinecone_index_name}'")
        print(f"📊 Index Statistics:")
        print(f"   - Total vectors: {stats.get('total_vector_count', 'N/A')}")
        print(f"   - Dimension: {stats.get('dimension', 'N/A')}")
        print(f"   - Index fullness: {stats.get('index_fullness', 'N/A')}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to connect to Pinecone: {e}")
        return False

def test_embedding_creation():
    """Test embedding creation with the configured model"""
    print("\n🧠 Testing Embedding Creation...")
    
    try:
        rag_service = RAGService()
        test_text = "This is a test document about Pakistani law and the 18th amendment."
        
        embedding = rag_service.vector_service.create_embedding(test_text)
        
        if embedding and len(embedding) == settings.embedding_dimension:
            print(f"✅ Successfully created {len(embedding)}-dimensional embedding")
            print(f"   - Expected dimension: {settings.embedding_dimension}")
            print(f"   - Actual dimension: {len(embedding)}")
            return True
        else:
            print(f"❌ Embedding creation failed or dimension mismatch")
            return False
    except Exception as e:
        print(f"❌ Error creating embedding: {e}")
        return False

def test_document_ingestion():
    """Test document ingestion into Pinecone"""
    print("\n📄 Testing Document Ingestion...")
    
    try:
        rag_service = RAGService()
        
        # Sample documents for testing
        test_documents = [
            {
                'id': 'test_doc_1',
                'content': 'The 18th Amendment to the Constitution of Pakistan was passed in 2010. It devolved several federal ministries to the provinces.',
                'metadata': {
                    'source': 'Constitution of Pakistan',
                    'section': '18th Amendment',
                    'document_type': 'constitution'
                }
            },
            {
                'id': 'test_doc_2',
                'content': 'Fundamental rights are guaranteed under Articles 8 to 28 of the Constitution of Pakistan. These rights protect citizens from arbitrary state action.',
                'metadata': {
                    'source': 'Constitution of Pakistan',
                    'section': 'Fundamental Rights',
                    'document_type': 'constitution'
                }
            }
        ]
        
        success = rag_service.ingest_documents(test_documents)
        
        if success:
            print("✅ Successfully ingested test documents")
            return True
        else:
            print("❌ Failed to ingest test documents")
            return False
    except Exception as e:
        print(f"❌ Error during document ingestion: {e}")
        return False

def test_search_functionality():
    """Test search functionality"""
    print("\n🔍 Testing Search Functionality...")
    
    try:
        rag_service = RAGService()
        
        # Test query
        query = "What is the 18th amendment?"
        results = rag_service.vector_service.search(query, top_k=3)
        
        print(f"✅ Search completed successfully")
        print(f"   - Query: '{query}'")
        print(f"   - Results found: {len(results)}")
        
        for i, result in enumerate(results[:2], 1):
            print(f"   - Result {i}: Score {result.score:.3f}")
            print(f"     Content: {result.content[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ Error during search: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Pinecone Integration Test Suite")
    print("=" * 50)
    print(f"Index Name: {settings.pinecone_index_name}")
    print(f"Host: {settings.pinecone_host}")
    print(f"Embedding Dimension: {settings.embedding_dimension}")
    print("=" * 50)
    
    tests = [
        test_pinecone_connection,
        test_embedding_creation,
        test_document_ingestion,
        test_search_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Pinecone integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check your configuration and try again.")
    
    return passed == total

if __name__ == "__main__":
    main() 