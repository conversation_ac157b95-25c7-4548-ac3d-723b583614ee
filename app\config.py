"""
Configuration settings for the Legal RAG API.

This module contains all configuration settings using Pydantic Settings
for environment variable management and validation.
"""

from pydantic_settings import BaseSettings
from typing import List, Optional


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # ============================================================================
    # Pinecone Configuration
    # ============================================================================
    pinecone_api_key: Optional[str] = None
    pinecone_api: Optional[str] = None  # Alternative name for backward compatibility
    pinecone_index_name: str = "legal-rag-index-2"
    pinecone_host: str = "https://legal-rag-index-2.svc.aped-4627-b74a.pinecone.io"
    
    # ============================================================================
    # LLM Configuration
    # ============================================================================
    google_api_key: Optional[str] = None
    gemini_model: str = "gemini-2.0-flash"
    
    # ============================================================================
    # Application Configuration
    # ============================================================================
    debug: bool = True
    port: int = 8000
    
    # ============================================================================
    # CORS Configuration
    # ============================================================================
    cors_origins: List[str] = [
        "http://localhost:3000",      # Vite default
        "http://localhost:5173",      # Vite alternative
        "http://127.0.0.1:3000",     # Alternative localhost
        "http://127.0.0.1:5173",     # Alternative localhost
        "http://localhost:8080",      # Common dev port
        "http://localhost:4173",      # Vite preview
        "http://localhost:4000",      # Common dev port
        "http://localhost:5000",      # Common dev port
        "http://localhost:3001",      # Alternative React port
        "http://localhost:4200",      # Angular default
        "http://localhost:8081",      # Vue CLI
        "http://localhost:9000",      # Another common port
        "*",                          # Allow all origins in development
    ]
    
    # ============================================================================
    # RAG Configuration
    # ============================================================================
    chunk_size: int = 512
    chunk_overlap: int = 50
    max_results: int = 5
    embedding_dimension: int = 384  # For sentence-transformers/all-MiniLM-L6-v2
    
    def __init__(self, **kwargs):
        """Initialize settings with backward compatibility."""
        super().__init__(**kwargs)
        # Handle backward compatibility for pinecone_api_key
        if self.pinecone_api_key is None and self.pinecone_api is not None:
            self.pinecone_api_key = self.pinecone_api
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()