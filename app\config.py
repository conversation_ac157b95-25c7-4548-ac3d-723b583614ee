# app/config.py
from pydantic_settings import BaseSettings
from typing import List


class Settings(BaseSettings):
    # Pinecone
    pinecone_api_key: str
    pinecone_index_name: str = "legal-rag-index"
    
    # Gemini
    google_api_key: str
    gemini_model: str = "gemini-1.5-flash"
    
    # App
    debug: bool = False
    port: int = 8000
    cors_origins: List[str] = ["http://localhost:3000"]
    
    # RAG Settings
    chunk_size: int = 512
    chunk_overlap: int = 50
    max_results: int = 5
    
    class Config:
        env_file = ".env"


settings = Settings()