import os
import sys
import uuid
from typing import List, Dict, Any

# Add parent directory to path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import only what we need for standalone ingestion
from app.models import DocumentType
from data_pipeline.processors import DocumentProcessor

# Standalone vector service for ingestion
import pinecone
from sentence_transformers import SentenceTransformer
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class StandaloneVectorService:
    """Minimal vector service for data ingestion without full app dependencies"""

    def __init__(self):
        # Initialize Pinecone
        api_key = os.getenv('PINECONE_API_KEY')
        index_name = os.getenv('PINECONE_INDEX_NAME', 'legal-rag-index')

        if not api_key:
            raise ValueError("PINECONE_API_KEY environment variable is required")

        pinecone.init(api_key=api_key)
        self.index = pinecone.Index(index_name)

        # Initialize embedding model
        print("Loading embedding model...")
        self.embedding_model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        print("Embedding model loaded successfully!")

    def create_embedding(self, text: str) -> List[float]:
        """Create embedding for text"""
        return self.embedding_model.encode(text).tolist()

    def upsert_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """Upsert documents to Pinecone"""
        try:
            vectors = []
            for doc in documents:
                embedding = self.create_embedding(doc['content'])
                vectors.append({
                    'id': doc['id'],
                    'values': embedding,
                    'metadata': doc['metadata']
                })

            # Batch upsert in chunks of 100 to avoid API limits
            batch_size = 100
            for i in range(0, len(vectors), batch_size):
                batch = vectors[i:i + batch_size]
                self.index.upsert(vectors=batch)
                print(f"Upserted batch {i//batch_size + 1}/{(len(vectors)-1)//batch_size + 1}")

            return True
        except Exception as e:
            print(f"Error upserting documents: {e}")
            return False


class DataIngestion:
    def __init__(self):
        self.processor = DocumentProcessor()
        self.vector_service = StandaloneVectorService()
    
    def ingest_file(self, file_path: str, document_type: DocumentType, source: str) -> bool:
        """Ingest a single file with improved chunking and metadata"""
        try:
            print(f"Processing {file_path}...")

            # Extract text based on file type
            if file_path.endswith('.pdf'):
                text = self.processor.process_pdf(file_path)
            elif file_path.endswith('.docx'):
                text = self.processor.process_docx(file_path)
            elif file_path.endswith('.txt'):
                text = self.processor.process_txt(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_path}")

            print(f"Extracted {len(text)} characters from {file_path}")

            # Clean text
            text = self.processor.clean_text(text)

            # Extract legal sections if possible
            sections = self.processor.extract_legal_sections(text, document_type)

            documents = []
            filename = os.path.basename(file_path)

            if sections:
                print(f"Found {len(sections)} legal sections")
                # Use extracted sections with enhanced metadata
                for section in sections:
                    # Extract additional metadata from section content
                    section_content = section['content']

                    # Create enhanced metadata
                    metadata = {
                        'source': source,
                        'section': section['section'],
                        'document_type': document_type.value,
                        'file_path': file_path,
                        'filename': filename,
                        'content': section_content,
                        'content_length': len(section_content),
                        'section_type': self._get_section_type(section['section'], document_type)
                    }

                    # Add chapter/part information if available
                    chapter_info = self._extract_chapter_info(section_content, document_type)
                    if chapter_info:
                        metadata.update(chapter_info)

                    doc = {
                        'id': str(uuid.uuid4()),
                        'content': section_content,
                        'metadata': metadata
                    }
                    documents.append(doc)
            else:
                print("No legal sections found, using intelligent chunking")
                # Use intelligent chunking for documents without clear sections
                chunks = self._intelligent_chunk_text(text, document_type)

                for i, chunk in enumerate(chunks):
                    metadata = {
                        'source': source,
                        'section': f"Chunk {i+1}",
                        'document_type': document_type.value,
                        'file_path': file_path,
                        'filename': filename,
                        'content': chunk,
                        'content_length': len(chunk),
                        'chunk_index': i,
                        'total_chunks': len(chunks),
                        'section_type': 'chunk'
                    }

                    # Try to identify what this chunk is about
                    chunk_context = self._identify_chunk_context(chunk, document_type)
                    if chunk_context:
                        metadata.update(chunk_context)

                    doc = {
                        'id': str(uuid.uuid4()),
                        'content': chunk,
                        'metadata': metadata
                    }
                    documents.append(doc)

            # Ingest into vector database
            print(f"Ingesting {len(documents)} documents into Pinecone...")
            success = self.vector_service.upsert_documents(documents)

            if success:
                print(f"✅ Successfully ingested {len(documents)} chunks from {file_path}")
                return True
            else:
                print(f"❌ Failed to ingest {file_path}")
                return False

        except Exception as e:
            print(f"❌ Error processing {file_path}: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _get_section_type(self, section_name: str, document_type: DocumentType) -> str:
        """Determine the type of legal section"""
        if document_type == DocumentType.PENAL_CODE:
            if 'Section' in section_name:
                return 'penal_section'
        elif document_type == DocumentType.CONSTITUTION:
            if 'Article' in section_name:
                return 'constitutional_article'
        return 'general_section'

    def _extract_chapter_info(self, content: str, document_type: DocumentType) -> Dict[str, str]:
        """Extract chapter/part information from content"""
        info = {}

        # Look for chapter information
        chapter_patterns = [
            r'CHAPTER\s+([IVX\d]+)[.\s]*[-–]?\s*([^\n]+)',
            r'Chapter\s+(\d+)[.\s]*[-–]?\s*([^\n]+)',
            r'PART\s+([IVX\d]+)[.\s]*[-–]?\s*([^\n]+)'
        ]

        for pattern in chapter_patterns:
            import re
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                info['chapter_number'] = match.group(1).strip()
                info['chapter_title'] = match.group(2).strip()
                break

        return info

    def _intelligent_chunk_text(self, text: str, document_type: DocumentType) -> List[str]:
        """Intelligent chunking that respects legal document structure"""
        # Use the existing processor but with better parameters for legal docs
        self.processor.chunk_size = 800  # Larger chunks for legal context
        self.processor.chunk_overlap = 100  # More overlap to preserve context

        # Try to split on natural boundaries first
        chunks = []

        # Split on double newlines (paragraph boundaries)
        paragraphs = text.split('\n\n')
        current_chunk = ""

        for paragraph in paragraphs:
            # If adding this paragraph would exceed chunk size, save current chunk
            if len(current_chunk) + len(paragraph) > self.processor.chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                # Start new chunk with overlap
                words = current_chunk.split()
                overlap_words = words[-self.processor.chunk_overlap//10:] if len(words) > self.processor.chunk_overlap//10 else words
                current_chunk = ' '.join(overlap_words) + ' ' + paragraph
            else:
                current_chunk += '\n\n' + paragraph if current_chunk else paragraph

        # Add the last chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        # If chunks are still too large, fall back to word-based chunking
        final_chunks = []
        for chunk in chunks:
            if len(chunk) > self.processor.chunk_size * 1.5:
                # Use original chunking method for oversized chunks
                sub_chunks = self.processor.chunk_text(chunk)
                final_chunks.extend(sub_chunks)
            else:
                final_chunks.append(chunk)

        return final_chunks

    def _identify_chunk_context(self, chunk: str, document_type: DocumentType) -> Dict[str, str]:
        """Identify what this chunk is about for better metadata"""
        context = {}

        # Look for key legal terms and concepts
        import re

        if document_type == DocumentType.PENAL_CODE:
            # Look for offense types
            offense_patterns = [
                r'murder|homicide|killing',
                r'theft|stealing|robbery',
                r'assault|battery|hurt',
                r'fraud|cheating|forgery',
                r'defamation|libel|slander'
            ]

            for pattern in offense_patterns:
                if re.search(pattern, chunk, re.IGNORECASE):
                    context['offense_category'] = pattern.split('|')[0]
                    break

        elif document_type == DocumentType.CONSTITUTION:
            # Look for rights categories
            rights_patterns = [
                r'fundamental rights?|basic rights?',
                r'freedom of|liberty',
                r'equality|equal protection',
                r'due process|fair trial',
                r'property rights?|ownership'
            ]

            for pattern in rights_patterns:
                if re.search(pattern, chunk, re.IGNORECASE):
                    context['rights_category'] = pattern.split('|')[0]
                    break

        return context

    def ingest_directory(self, directory_path: str, document_type: DocumentType, source: str):
        """Ingest all files in a directory"""
        print(f"Scanning directory: {directory_path}")
        files_found = []

        for filename in os.listdir(directory_path):
            if filename.endswith(('.pdf', '.docx', '.txt')):
                files_found.append(filename)

        print(f"Found {len(files_found)} files to process: {files_found}")

        for filename in files_found:
            file_path = os.path.join(directory_path, filename)
            print(f"\n{'='*60}")
            success = self.ingest_file(file_path, document_type, source)
            if not success:
                print(f"⚠️  Failed to process {filename}")


# Main execution script
if __name__ == "__main__":
    print("🚀 Starting Legal Document Ingestion")
    print("="*60)

    try:
        ingestion = DataIngestion()

        # Ingest Pakistan Penal Code
        print("\n📖 Processing Pakistan Penal Code...")
        success1 = ingestion.ingest_file(
            "data/raw/Pakistan_Penal_Code.pdf",
            DocumentType.PENAL_CODE,
            "Pakistan Penal Code 1860"
        )

        # Ingest Constitution (Fundamental Rights)
        print("\n📜 Processing Constitution (Fundamental Rights)...")
        success2 = ingestion.ingest_file(
            "data/raw/fundamental_rights_from_constituion.pdf",
            DocumentType.CONSTITUTION,
            "Constitution of Pakistan 1973 - Fundamental Rights"
        )

        print("\n" + "="*60)
        print("📊 INGESTION SUMMARY")
        print("="*60)
        print(f"Pakistan Penal Code: {'✅ Success' if success1 else '❌ Failed'}")
        print(f"Constitution: {'✅ Success' if success2 else '❌ Failed'}")

        if success1 and success2:
            print("\n🎉 All documents successfully ingested into Pinecone!")
            print("Your vector database is ready for RAG queries.")
        else:
            print("\n⚠️  Some documents failed to ingest. Check the logs above.")

    except Exception as e:
        print(f"\n❌ Fatal error during ingestion: {e}")
        import traceback
        traceback.print_exc()