import os
import sys
import uuid

# Add parent directory to path to import app modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services import RAGService
from app.models import DocumentType
from data_pipeline.processors import DocumentProcessor


class DataIngestion:
    def __init__(self):
        self.processor = DocumentProcessor()
        self.rag_service = RAGService()
    
    def ingest_file(self, file_path: str, document_type: DocumentType, source: str) -> bool:
        """Ingest a single file"""
        try:
            print(f"Processing {file_path}...")
            
            # Extract text based on file type
            if file_path.endswith('.pdf'):
                text = self.processor.process_pdf(file_path)
            elif file_path.endswith('.docx'):
                text = self.processor.process_docx(file_path)
            elif file_path.endswith('.txt'):
                text = self.processor.process_txt(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_path}")
            
            # Clean text
            text = self.processor.clean_text(text)
            
            # Extract legal sections if possible
            sections = self.processor.extract_legal_sections(text, document_type)
            
            documents = []
            
            if sections:
                # Use extracted sections
                for section in sections:
                    doc = {
                        'id': str(uuid.uuid4()),
                        'content': section['content'],
                        'metadata': {
                            'source': source,
                            'section': section['section'],
                            'document_type': document_type.value,
                            'file_path': file_path,
                            'content': section['content']  # Store in metadata for search results
                        }
                    }
                    documents.append(doc)
            else:
                # Use text chunks
                chunks = self.processor.chunk_text(text)
                for i, chunk in enumerate(chunks):
                    doc = {
                        'id': str(uuid.uuid4()),
                        'content': chunk,
                        'metadata': {
                            'source': source,
                            'section': f"Chunk {i+1}",
                            'document_type': document_type.value,
                            'file_path': file_path,
                            'content': chunk
                        }
                    }
                    documents.append(doc)
            
            # Ingest into vector database
            success = self.rag_service.ingest_documents(documents)
            
            if success:
                print(f"Successfully ingested {len(documents)} chunks from {file_path}")
                return True
            else:
                print(f"Failed to ingest {file_path}")
                return False
        
        except Exception as e:
            print(f"Error processing {file_path}: {e}")
            return False
    
    def ingest_directory(self, directory_path: str, document_type: DocumentType, source: str):
        """Ingest all files in a directory"""
        for filename in os.listdir(directory_path):
            if filename.endswith(('.pdf', '.docx', '.txt')):
                file_path = os.path.join(directory_path, filename)
                self.ingest_file(file_path, document_type, source)


# Example usage script
if __name__ == "__main__":
    ingestion = DataIngestion()
    
    # Example: Ingest Pakistan Penal Code
    # ingestion.ingest_file(
    #     "data/raw/pakistan_penal_code.pdf", 
    #     DocumentType.PENAL_CODE, 
    #     "Pakistan Penal Code 1860"
    # )
    
    # Example: Ingest Constitution
    # ingestion.ingest_file(
    #     "data/raw/constitution.pdf", 
    #     DocumentType.CONSTITUTION, 
    #     "Constitution of Pakistan 1973"
    # )
    
    print("Data ingestion script ready. Uncomment examples to run.")