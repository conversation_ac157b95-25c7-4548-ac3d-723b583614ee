from document_ingester import DocumentIngester


# Main script
if __name__ == "__main__":
    print("🚀 Legal Document Ingestion")
    print("=" * 50)
    
    try:
        ingester = DocumentIngester()
        
        # Ingest Pakistan Penal Code
        print("\n📖 Ingesting Pakistan Penal Code...")
        success1 = ingester.ingest_pdf(
            "../data/raw/Pakistan_Penal_Code.pdf",
            "Pakistan Penal Code 1860",
            "penal_code"
        )
        
        # Ingest Constitution
        print("\n📜 Ingesting Constitution...")
        success2 = ingester.ingest_pdf(
            "../data/raw/fundamental_rights_from_constituion.pdf",
            "Constitution of Pakistan 1973 - Fundamental Rights",
            "constitution"
        )
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 SUMMARY")
        print("=" * 50)
        print(f"Penal Code: {'✅ Success' if success1 else '❌ Failed'}")
        print(f"Constitution: {'✅ Success' if success2 else '❌ Failed'}")
        
        if success1 and success2:
            print("\n🎉 All documents ingested successfully!")
        else:
            print("\n⚠️ Some documents failed to ingest")
            
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
