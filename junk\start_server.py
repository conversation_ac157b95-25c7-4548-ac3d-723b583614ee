#!/usr/bin/env python3
"""
Simple script to start the FastAPI server
"""

import uvicorn
from app.main import app

if __name__ == "__main__":
    print("🚀 Starting Legal RAG API Server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/api/v1/health")
    print("=" * 50)
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True
    ) 