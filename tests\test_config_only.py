#!/usr/bin/env python3
"""
Test configuration loading only
"""

import sys
import os
from dotenv import load_dotenv

def test_config():
    """Test if configuration can be loaded"""
    print("⚙️  Testing Configuration...")
    
    try:
        # Load environment variables
        load_dotenv()
        
        # Add app directory to path
        sys.path.append('app')
        
        # Import and test config
        from app.config import settings
        
        print("✅ Configuration loaded successfully!")
        print(f"   - Pinecone Index: {settings.pinecone_index_name}")
        print(f"   - Pinecone Host: {settings.pinecone_host}")
        print(f"   - Embedding Dimension: {settings.embedding_dimension}")
        
        # Check API keys
        if settings.pinecone_api_key:
            print(f"   - Pinecone API Key: {'*' * 10}{settings.pinecone_api_key[-4:]}")
        else:
            print("   - Pinecone API Key: ❌ Missing")
        
        if settings.google_api_key:
            print(f"   - Google API Key: {'*' * 10}{settings.google_api_key[-4:]}")
        else:
            print("   - Google API Key: ❌ Missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Run configuration test"""
    print("🔧 Configuration Test")
    print("=" * 30)
    
    success = test_config()
    
    if success:
        print("\n✅ Configuration is working!")
        print("You can now run the full tests.")
    else:
        print("\n❌ Configuration has issues.")
        print("Please run: python setup_env.py")
    
    return success

if __name__ == "__main__":
    main() 