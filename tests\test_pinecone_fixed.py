#!/usr/bin/env python3
"""
Simple test to verify Pinecone import and initialization works
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_pinecone_import():
    """Test that Pinecone can be imported correctly"""
    print("🔍 Testing Pinecone Import...")
    
    try:
        from pinecone import Pinecone
        print("✅ Pinecone imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Failed to import Pinecone: {e}")
        return False

def test_pinecone_initialization():
    """Test that Pinecone can be initialized with API key"""
    print("\n🔑 Testing Pinecone Initialization...")
    
    try:
        from pinecone import Pinecone
        
        api_key = os.getenv('PINECONE_API_KEY')
        if not api_key:
            print("❌ PINECONE_API_KEY not found in environment")
            return False
        
        print(f"✅ Found API key: {'*' * (len(api_key) - 4)}{api_key[-4:]}")
        
        pc = Pinecone(api_key=api_key)
        print("✅ Pinecone client initialized successfully")
        
        return True
    except Exception as e:
        print(f"❌ Failed to initialize Pinecone: {e}")
        return False

def test_index_connection():
    """Test connection to the Pinecone index"""
    print("\n📊 Testing Index Connection...")
    
    try:
        from pinecone import Pinecone
        
        api_key = os.getenv('PINECONE_API_KEY')
        index_name = os.getenv('PINECONE_INDEX_NAME', 'legal-rag-index-2')
        
        pc = Pinecone(api_key=api_key)
        index = pc.Index(index_name)
        
        # Get index stats
        stats = index.describe_index_stats()
        print(f"✅ Connected to index: {index_name}")
        print(f"   - Total vectors: {stats.total_vector_count}")
        print(f"   - Dimension: {stats.dimension}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to connect to index: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Pinecone Fix Verification Test")
    print("=" * 40)
    
    tests = [
        test_pinecone_import,
        test_pinecone_initialization,
        test_index_connection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Pinecone is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
    
    return passed == total

if __name__ == "__main__":
    main() 