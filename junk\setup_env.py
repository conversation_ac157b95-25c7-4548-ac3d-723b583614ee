#!/usr/bin/env python3
"""
Setup environment file with correct variable names
"""

import os

def create_env_file():
    """Create or update .env file with correct variable names"""
    
    print("🔧 Setting up Environment File")
    print("=" * 40)
    
    # Check if .env file exists
    env_file = ".env"
    env_content = []
    
    if os.path.exists(env_file):
        print("📄 Found existing .env file")
        with open(env_file, 'r') as f:
            existing_content = f.read()
            print("Current content:")
            print(existing_content)
    else:
        print("📄 Creating new .env file")
    
    print("\n🔑 Please provide your API keys:")
    
    # Get Pinecone API key
    pinecone_key = input("Enter your Pinecone API key (or press Enter if already in .env): ").strip()
    
    # Get Google API key
    google_key = input("Enter your Google API key (or press Enter if already in .env): ").strip()
    
    # Build new content
    env_content.append("# Pinecone Configuration")
    if pinecone_key:
        env_content.append(f"PINECONE_API_KEY={pinecone_key}")
    else:
        env_content.append("PINECONE_API_KEY=your_pinecone_api_key_here")
    
    env_content.append("PINECONE_INDEX_NAME=atara")
    env_content.append("PINECONE_HOST=https://atara-lcbpurk.svc.aped-4627-b74a.pinecone.io")
    env_content.append("")
    
    env_content.append("# Google Gemini (for LLM responses)")
    if google_key:
        env_content.append(f"GOOGLE_API_KEY={google_key}")
    else:
        env_content.append("GOOGLE_API_KEY=your_google_api_key_here")
    
    env_content.append("")
    env_content.append("# App Configuration")
    env_content.append("DEBUG=false")
    env_content.append("PORT=8000")
    
    # Write to file
    with open(env_file, 'w') as f:
        f.write('\n'.join(env_content))
    
    print(f"\n✅ Environment file created/updated: {env_file}")
    print("\n📋 Current .env content:")
    print("=" * 40)
    with open(env_file, 'r') as f:
        print(f.read())
    
    print("\n🚀 Next steps:")
    print("1. Make sure your API keys are correct in the .env file")
    print("2. Run: python test_basic.py")
    print("3. Run: python example_usage.py")

if __name__ == "__main__":
    create_env_file() 