"""
Vector service for Pinecone operations.

This module handles all vector database operations including embeddings,
search, and document upserting.
"""

import os
import logging
import numpy as np
from typing import List, Dict, Any
from pinecone import Pinecone
from sentence_transformers import SentenceTransformer

from app.config import settings
from app.models import SearchResult, DocumentType

logger = logging.getLogger(__name__)


class VectorService:
    """Service for handling vector database operations with Pinecone."""
    
    def __init__(self):
        """Initialize the vector service with Pinecone and embedding model."""
        self._initialize_pinecone()
        self._initialize_embedding_model()
    
    def _initialize_pinecone(self) -> None:
        """Initialize Pinecone connection."""
        try:
            api_key = settings.pinecone_api_key
            index_name = settings.pinecone_index_name
            
            if not api_key:
                logger.error("PINECONE_API_KEY not configured")
                self.index = None
                return
            
            logger.info(f"Connecting to Pinecone index: {index_name}")
            self.pc = Pinecone(api_key=api_key)
            self.index = self.pc.Index(index_name)
            logger.info("✅ Pinecone index connected successfully")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize Pinecone: {e}")
            self.index = None
    
    def _initialize_embedding_model(self) -> None:
        """Initialize the embedding model."""
        try:
            self.embedding_model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
            logger.info("✅ Embedding model loaded successfully")
        except Exception as e:
            logger.error(f"❌ Failed to load embedding model: {e}")
            self.embedding_model = None
    
    def create_embedding(self, text: str) -> List[float]:
        """
        Create embedding for text using the configured model.
        
        Args:
            text: Input text to embed
            
        Returns:
            List of floats representing the embedding
        """
        try:
            if not self.embedding_model:
                logger.warning("Embedding model not available, returning mock embedding")
                return [0.1] * settings.embedding_dimension
            
            # Truncate text if too long (rough estimate for token limit)
            if len(text) > 8000:
                text = text[:8000]
                logger.debug("Text truncated to 8000 characters")
            
            embedding = self.embedding_model.encode(text)
            
            # Ensure embedding matches expected dimension
            if len(embedding) != settings.embedding_dimension:
                logger.warning(f"Embedding dimension mismatch: {len(embedding)} != {settings.embedding_dimension}")
                if len(embedding) > settings.embedding_dimension:
                    embedding = embedding[:settings.embedding_dimension]
                else:
                    embedding = np.pad(embedding, (0, settings.embedding_dimension - len(embedding)))
            
            return embedding.tolist()
            
        except Exception as e:
            logger.error(f"Error creating embedding: {e}")
            return [0.1] * settings.embedding_dimension
    
    def upsert_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """
        Upsert documents to Pinecone with proper metadata.
        
        Args:
            documents: List of documents with content and metadata
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if not self.index:
                logger.warning("Pinecone index not available, skipping document ingestion")
                return False
            
            vectors = []
            for doc in documents:
                embedding = self.create_embedding(doc['content'])
                if not embedding:
                    continue
                
                # Use the same metadata structure as document_ingester.py
                metadata = doc.get('metadata', {})
                
                vectors.append({
                    'id': doc['id'],
                    'values': embedding,
                    'metadata': metadata
                })
            
            if vectors:
                # Upload in batches for better performance
                batch_size = 100
                for i in range(0, len(vectors), batch_size):
                    batch = vectors[i:i + batch_size]
                    self.index.upsert(vectors=batch)
                    logger.info(f"Uploaded batch {i//batch_size + 1}")
                
                logger.info(f"Successfully upserted {len(vectors)} documents")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error upserting documents: {e}")
            return False
    
    def search(self, query: str, top_k: int = 5) -> List[SearchResult]:
        """
        Search for similar documents using cosine similarity.
        
        Args:
            query: Search query text
            top_k: Number of results to return
            
        Returns:
            List of SearchResult objects
        """
        try:
            if not self.index:
                logger.warning("Pinecone index not available, returning mock results")
                return self._get_mock_results(query, top_k)
            
            if not self.embedding_model:
                logger.warning("Embedding model not available, returning mock results")
                return self._get_mock_results(query, top_k)
            
            query_embedding = self.create_embedding(query)
            if not query_embedding:
                logger.error("Failed to create query embedding")
                return self._get_mock_results(query, top_k)
            
            response = self.index.query(
                vector=query_embedding,
                top_k=top_k,
                include_metadata=True
            )
            
            results = []
            for match in response.matches:
                metadata = match.metadata
                content = metadata.get('content', '')
                
                # Map document_type to DocumentType enum
                doc_type_str = metadata.get('document_type', 'other')
                try:
                    doc_type = DocumentType(doc_type_str)
                except ValueError:
                    doc_type = DocumentType.OTHER
                
                result = SearchResult(
                    content=content,
                    source=metadata.get('source', ''),
                    section=metadata.get('section_number', metadata.get('article_number', '')),
                    score=match.score,
                    document_type=doc_type
                )
                results.append(result)
            
            logger.info(f"Found {len(results)} results for query: {query}")
            return results
            
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return self._get_mock_results(query, top_k)
    
    def _get_mock_results(self, query: str, top_k: int) -> List[SearchResult]:
        """
        Return mock results for testing when Pinecone is not available.
        
        Args:
            query: Search query
            top_k: Number of results
            
        Returns:
            List of mock SearchResult objects
        """
        mock_results = [
            SearchResult(
                content=f"Mock result for query: {query}. This is a placeholder response for testing purposes.",
                source="mock_source",
                section="mock_section",
                score=0.95,
                document_type=DocumentType.OTHER
            ),
            SearchResult(
                content="This is a second mock result to simulate multiple search results.",
                source="mock_source_2",
                section="mock_section_2", 
                score=0.85,
                document_type=DocumentType.OTHER
            )
        ]
        return mock_results[:top_k]
    
    def get_index_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the Pinecone index.
        
        Returns:
            Dictionary containing index statistics
        """
        try:
            if not self.index:
                logger.warning("Pinecone index not available")
                return {}
            
            stats = self.index.describe_index_stats()
            return {
                'total_vector_count': stats.total_vector_count,
                'dimension': stats.dimension,
                'index_fullness': stats.index_fullness,
                'namespaces': stats.namespaces
            }
        except Exception as e:
            logger.error(f"Error getting index stats: {e}")
            return {} 