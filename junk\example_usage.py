#!/usr/bin/env python3
"""
Example usage of Pinecone integration for storing and querying vector embeddings
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add app directory to path
sys.path.append('app')

from app.services import RAGService

def example_document_ingestion():
    """Example of ingesting documents into Pinecone"""
    print("📄 Example: Document Ingestion")
    print("-" * 40)
    
    rag_service = RAGService()
    
    # Sample legal documents
    documents = [
        {
            'id': 'constitution_article_8',
            'content': 'Article 8 of the Constitution of Pakistan states that any law, or any custom or usage having the force of law, in so far as it is inconsistent with the rights conferred by this Chapter, shall, to the extent of such inconsistency, be void.',
            'metadata': {
                'source': 'Constitution of Pakistan',
                'section': 'Article 8',
                'document_type': 'constitution',
                'topic': 'fundamental rights'
            }
        },
        {
            'id': 'ppc_section_302',
            'content': 'Section 302 of the Pakistan Penal Code defines murder as causing death of a person with the intention of causing death, or with the intention of causing such bodily injury as is likely to cause death.',
            'metadata': {
                'source': 'Pakistan Penal Code',
                'section': 'Section 302',
                'document_type': 'penal_code',
                'topic': 'criminal law'
            }
        },
        {
            'id': '18th_amendment_summary',
            'content': 'The 18th Amendment to the Constitution of Pakistan, passed in 2010, was a landmark constitutional amendment that devolved several federal ministries to the provinces, including education, health, and agriculture.',
            'metadata': {
                'source': 'Constitution of Pakistan',
                'section': '18th Amendment',
                'document_type': 'constitution',
                'topic': 'constitutional amendments'
            }
        }
    ]
    
    # Ingest documents
    success = rag_service.ingest_documents(documents)
    
    if success:
        print("✅ Documents successfully ingested into Pinecone")
        print(f"   - Ingested {len(documents)} documents")
    else:
        print("❌ Failed to ingest documents")
    
    return success

def example_search_queries():
    """Example of searching documents using different queries"""
    print("\n🔍 Example: Search Queries")
    print("-" * 40)
    
    rag_service = RAGService()
    
    # Example queries
    queries = [
        "What is the 18th amendment?",
        "What does Article 8 of the Constitution say?",
        "How is murder defined in Pakistani law?",
        "What are fundamental rights in Pakistan?"
    ]
    
    for query in queries:
        print(f"\nQuery: '{query}'")
        
        # Search for relevant documents
        search_results = rag_service.vector_service.search(query, top_k=2)
        
        if search_results:
            print(f"Found {len(search_results)} relevant documents:")
            for i, result in enumerate(search_results, 1):
                print(f"  {i}. Score: {result.score:.3f}")
                print(f"     Source: {result.source} - {result.section}")
                print(f"     Content: {result.content[:150]}...")
        else:
            print("No relevant documents found")
    
    return True

def example_rag_query():
    """Example of using RAG to generate answers"""
    print("\n🤖 Example: RAG Query with LLM")
    print("-" * 40)
    
    rag_service = RAGService()
    
    # Example question
    question = "What is the 18th amendment and why is it important?"
    
    print(f"Question: {question}")
    
    # Get RAG response
    answer, search_results = rag_service.query(question)
    
    print(f"\nAnswer: {answer}")
    
    if search_results:
        print(f"\nSources used ({len(search_results)} documents):")
        for i, result in enumerate(search_results, 1):
            print(f"  {i}. {result.source} - {result.section} (Score: {result.score:.3f})")
    
    return True

def example_index_statistics():
    """Example of getting index statistics"""
    print("\n📊 Example: Index Statistics")
    print("-" * 40)
    
    rag_service = RAGService()
    
    # Get index stats
    stats = rag_service.get_index_stats()
    
    print("Pinecone Index Statistics:")
    print(f"  - Total vectors: {stats.get('total_vector_count', 'N/A')}")
    print(f"  - Dimension: {stats.get('dimension', 'N/A')}")
    print(f"  - Index fullness: {stats.get('index_fullness', 'N/A')}")
    
    if stats.get('namespaces'):
        print("  - Namespaces:")
        for namespace, count in stats['namespaces'].items():
            print(f"    * {namespace}: {count} vectors")
    
    return True

def main():
    """Run all examples"""
    print("🚀 Pinecone Integration Examples")
    print("=" * 50)
    
    examples = [
        example_document_ingestion,
        example_search_queries,
        example_rag_query,
        example_index_statistics
    ]
    
    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"❌ Error in example: {e}")
        print("\n" + "=" * 50)

if __name__ == "__main__":
    main() 