# app/models.py
from pydantic import BaseModel
from typing import List, Optional
from enum import Enum


class DocumentType(str, Enum):
    CONSTITUTION = "constitution"
    PENAL_CODE = "penal_code"
    PAKISTAN_CODE = "pakistan_code"
    OTHER = "other"


class ChatRequest(BaseModel):
    query: str
    conversation_id: Optional[str] = None


class SearchResult(BaseModel):
    content: str
    source: str
    section: str
    score: float
    document_type: DocumentType


class ChatResponse(BaseModel):
    answer: str
    sources: List[SearchResult]
    conversation_id: str


class HealthResponse(BaseModel):
    status: str
    version: str = "1.0.0"


class IngestRequest(BaseModel):
    file_path: str
    document_type: DocumentType
    source: str


class IngestResponse(BaseModel):
    message: str
    chunks_created: int
    success: bool