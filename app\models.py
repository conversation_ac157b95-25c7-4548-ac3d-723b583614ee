"""
Data models for the Legal RAG API.

This module contains Pydantic models for request/response validation
and data structures used throughout the application.
"""

from pydantic import BaseModel, Field
from typing import List, Optional
from enum import Enum


class DocumentType(str, Enum):
    """Enumeration of supported document types."""
    CONSTITUTION = "constitution"
    PENAL_CODE = "penal_code"
    OTHER = "other"


class SearchResult(BaseModel):
    """Model for search results from vector database."""
    content: str = Field(..., description="The content of the search result")
    source: str = Field(..., description="Source document name")
    section: str = Field(default="", description="Section or article number")
    score: float = Field(..., description="Similarity score (0-1)")
    document_type: DocumentType = Field(..., description="Type of legal document")


class ChatRequest(BaseModel):
    """Model for chat request."""
    query: str = Field(..., min_length=1, description="User's legal question")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for context")


class ChatResponse(BaseModel):
    """Model for chat response."""
    answer: str = Field(..., description="Generated answer from LLM")
    sources: List[SearchResult] = Field(..., description="Source documents used")
    conversation_id: str = Field(..., description="Conversation ID")
    query: str = Field(..., description="Original user query")
    processing_time: float = Field(..., description="Time taken to process request")


class ChatErrorResponse(BaseModel):
    """Model for chat error responses."""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    conversation_id: str = Field(..., description="Conversation ID")


class HealthResponse(BaseModel):
    """Model for health check response."""
    status: str = Field(..., description="Health status")


class IngestRequest(BaseModel):
    """Model for document ingestion request."""
    file_path: str = Field(..., description="Path to document file")


class IngestResponse(BaseModel):
    """Model for document ingestion response."""
    message: str = Field(..., description="Response message")
    chunks_created: int = Field(..., description="Number of chunks created")
    documents_processed: Optional[int] = Field(None, description="Number of documents processed")
    success: bool = Field(..., description="Whether ingestion was successful")


class DocumentIngestRequest(BaseModel):
    """Model for direct document ingestion."""
    documents: List[dict] = Field(..., description="List of documents to ingest")


class SystemStats(BaseModel):
    """Model for system statistics."""
    total_vectors: int = Field(..., description="Total vectors in index")
    index_dimension: int = Field(..., description="Vector dimension")
    index_fullness: float = Field(..., description="Index fullness percentage")
    last_updated: Optional[str] = Field(None, description="Last update timestamp")