"""
RAG (Retrieval-Augmented Generation) service.

This module orchestrates the vector search and LLM generation to provide
comprehensive legal document querying capabilities.
"""

import logging
from typing import List, Dict, Any, Tuple

from app.config import settings
from app.models import SearchResult
from .vector_service import VectorService
from .llm_service import LLMService

logger = logging.getLogger(__name__)


class RAGService:
    """
    Main RAG service that coordinates vector search and LLM generation.
    
    This service combines the power of semantic search with LLM generation
    to provide accurate and contextual legal responses.
    """
    
    def __init__(self):
        """Initialize the RAG service with vector and LLM services."""
        self.vector_service = VectorService()
        self.llm_service = LLMService()
    
    def query(self, user_query: str) -> Tuple[str, List[SearchResult]]:
        """
        Process a user query through the complete RAG pipeline.
        
        This method performs the following steps:
        1. Search for relevant documents using vector similarity
        2. Generate a contextual response using the LLM
        3. Return both the answer and the source documents
        
        Args:
            user_query: The user's legal question
            
        Returns:
            Tuple of (answer, search_results)
        """
        # Search for relevant documents
        search_results = self.vector_service.search(
            query=user_query, 
            top_k=settings.max_results
        )
        
        # Generate response using LLM
        answer = self.llm_service.generate_response(user_query, search_results)
        
        return answer, search_results
    
    def ingest_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """
        Ingest documents into the vector database.
        
        Args:
            documents: List of documents with content and metadata
            
        Returns:
            True if successful, False otherwise
        """
        return self.vector_service.upsert_documents(documents)
    
    def get_index_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the Pinecone index.
        
        Returns:
            Dictionary containing index statistics
        """
        return self.vector_service.get_index_stats()
    
    def search_only(self, query: str, top_k: int = None) -> List[SearchResult]:
        """
        Perform search only without LLM generation.
        
        Args:
            query: Search query
            top_k: Number of results (uses default if None)
            
        Returns:
            List of search results
        """
        if top_k is None:
            top_k = settings.max_results
        
        return self.vector_service.search(query, top_k)
    
    def generate_only(self, query: str, context: List[SearchResult]) -> str:
        """
        Generate LLM response only without performing search.
        
        Args:
            query: User's question
            context: Pre-existing search results
            
        Returns:
            Generated response
        """
        return self.llm_service.generate_response(query, context) 