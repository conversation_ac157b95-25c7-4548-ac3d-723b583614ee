# Frontend Integration Guide

This guide shows how to integrate your React chatbot frontend with the FastAPI backend.

## 🚀 API Endpoints

### Main Chat Endpoint
```javascript
POST /api/v1/chat
```

**Request Body:**
```json
{
  "query": "What is the 18th amendment?",
  "conversation_id": "optional-uuid",
  "top_k": 5
}
```

**Response:**
```json
{
  "answer": "The 18th Amendment to the Constitution of Pakistan...",
  "sources": [
    {
      "content": "The 18th Amendment was passed in 2010...",
      "source": "Constitution of Pakistan",
      "section": "18th Amendment",
      "score": 0.95,
      "document_type": "constitutional"
    }
  ],
  "conversation_id": "uuid-here",
  "query": "What is the 18th amendment?",
  "processing_time": 2.34
}
```

### Search Endpoint (Documents Only)
```javascript
GET /api/v1/search?query=18th amendment&top_k=5
```

### Health Check
```javascript
GET /api/v1/health
```

## 📱 React Integration Example

### 1. API Service

Create `src/services/api.js`:

```javascript
const API_BASE = 'http://localhost:8000/api/v1';

export const chatAPI = {
  // Send chat message
  async sendMessage(query, conversationId = null, topK = 5) {
    try {
      const response = await fetch(`${API_BASE}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          conversation_id: conversationId,
          top_k: topK
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Chat API error:', error);
      throw error;
    }
  },

  // Search documents only
  async searchDocuments(query, topK = 5) {
    try {
      const response = await fetch(
        `${API_BASE}/search?query=${encodeURIComponent(query)}&top_k=${topK}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Search API error:', error);
      throw error;
    }
  },

  // Health check
  async healthCheck() {
    try {
      const response = await fetch(`${API_BASE}/health`);
      return await response.json();
    } catch (error) {
      console.error('Health check error:', error);
      throw error;
    }
  }
};
```

### 2. Chat Component

Create `src/components/Chat.jsx`:

```jsx
import React, { useState, useRef, useEffect } from 'react';
import { chatAPI } from '../services/api';

const Chat = () => {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [conversationId, setConversationId] = useState(null);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      const response = await chatAPI.sendMessage(inputValue, conversationId);
      
      const botMessage = {
        id: Date.now() + 1,
        type: 'bot',
        content: response.answer,
        sources: response.sources,
        timestamp: new Date().toISOString(),
        processingTime: response.processing_time
      };

      setMessages(prev => [...prev, botMessage]);
      setConversationId(response.conversation_id);
    } catch (error) {
      const errorMessage = {
        id: Date.now() + 1,
        type: 'error',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="chat-container">
      <div className="chat-header">
        <h2>Legal Assistant</h2>
        <p>Ask questions about Pakistani law</p>
      </div>

      <div className="messages-container">
        {messages.map((message) => (
          <div key={message.id} className={`message ${message.type}`}>
            <div className="message-content">
              {message.content}
            </div>
            
            {message.sources && message.sources.length > 0 && (
              <div className="sources">
                <h4>Sources:</h4>
                {message.sources.map((source, index) => (
                  <div key={index} className="source">
                    <strong>{source.source} - {source.section}</strong>
                    <p>{source.content.substring(0, 150)}...</p>
                    <small>Relevance: {(source.score * 100).toFixed(1)}%</small>
                  </div>
                ))}
              </div>
            )}
            
            {message.processingTime && (
              <small className="processing-time">
                Processed in {message.processingTime.toFixed(2)}s
              </small>
            )}
            
            <div className="message-timestamp">
              {new Date(message.timestamp).toLocaleTimeString()}
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="message bot loading">
            <div className="typing-indicator">
              <span></span>
              <span></span>
              <span></span>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      <div className="input-container">
        <textarea
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Ask a question about Pakistani law..."
          disabled={isLoading}
          rows={1}
        />
        <button 
          onClick={handleSendMessage}
          disabled={isLoading || !inputValue.trim()}
        >
          {isLoading ? 'Sending...' : 'Send'}
        </button>
      </div>
    </div>
  );
};

export default Chat;
```

### 3. CSS Styling

Create `src/components/Chat.css`:

```css
.chat-container {
  max-width: 800px;
  margin: 0 auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.chat-header {
  background: #2c3e50;
  color: white;
  padding: 1rem;
  text-align: center;
}

.chat-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.chat-header p {
  margin: 0.5rem 0 0 0;
  opacity: 0.8;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.message {
  max-width: 70%;
  padding: 1rem;
  border-radius: 1rem;
  position: relative;
}

.message.user {
  align-self: flex-end;
  background: #3498db;
  color: white;
}

.message.bot {
  align-self: flex-start;
  background: white;
  border: 1px solid #ddd;
}

.message.error {
  align-self: center;
  background: #e74c3c;
  color: white;
  max-width: 90%;
}

.message-content {
  margin-bottom: 0.5rem;
}

.sources {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.sources h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #666;
}

.source {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 0.5rem;
  font-size: 0.8rem;
}

.source strong {
  color: #2c3e50;
}

.source p {
  margin: 0.25rem 0;
  color: #666;
}

.source small {
  color: #999;
}

.processing-time {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.7rem;
  color: #999;
}

.message-timestamp {
  font-size: 0.7rem;
  opacity: 0.7;
  margin-top: 0.25rem;
}

.typing-indicator {
  display: flex;
  gap: 0.25rem;
  padding: 0.5rem;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.input-container {
  padding: 1rem;
  background: white;
  border-top: 1px solid #ddd;
  display: flex;
  gap: 0.5rem;
}

.input-container textarea {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 0.5rem;
  resize: none;
  font-family: inherit;
}

.input-container button {
  padding: 0.75rem 1.5rem;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: bold;
}

.input-container button:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.input-container button:hover:not(:disabled) {
  background: #2980b9;
}
```

## 🔧 Setup Instructions

### 1. Start the FastAPI Backend
```bash
# Install dependencies first
python install_dependencies.py

# Start the server
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 2. Test the API
```bash
python test_api.py
```

### 3. Configure CORS
The FastAPI app is already configured with CORS for `http://localhost:3000`. If your React app runs on a different port, update the `cors_origins` in `app/config.py`.

### 4. Environment Variables
Make sure your `.env` file has the required API keys:
```env
PINECONE_API_KEY=your_pinecone_api_key
GOOGLE_API_KEY=your_google_api_key
```

## 🎯 Key Features

- **Real-time Chat**: Send messages and get responses with sources
- **Source Attribution**: Each response includes relevant legal sources
- **Conversation Tracking**: Maintains conversation context
- **Error Handling**: Graceful error handling and user feedback
- **Loading States**: Visual feedback during processing
- **Responsive Design**: Works on desktop and mobile

## 🚀 Deployment

For production deployment:

1. **Backend**: Deploy FastAPI to your preferred hosting service
2. **Frontend**: Build and deploy React app to a static hosting service
3. **CORS**: Update CORS origins to include your production domain
4. **Environment**: Set production environment variables

## 📞 Support

If you encounter issues:
1. Check the API health endpoint
2. Verify your API keys are set correctly
3. Check the browser console for errors
4. Test the API endpoints directly with curl or Postman 