#!/usr/bin/env python3
"""
Basic test script to verify core functionality
"""

import sys
import os

def test_imports():
    """Test if core modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import numpy as np
        print("✅ numpy imported successfully")
    except ImportError as e:
        print(f"❌ numpy import failed: {e}")
        return False
    
    try:
        from pinecone import Pinecone
        print("✅ pinecone imported successfully")
    except ImportError as e:
        print(f"❌ pinecone import failed: {e}")
        return False
    
    try:
        import google.generativeai as genai
        print("✅ google.generativeai imported successfully")
    except ImportError as e:
        print(f"❌ google.generativeai import failed: {e}")
        return False
    
    try:
        from sentence_transformers import SentenceTransformer
        print("✅ sentence_transformers imported successfully")
    except ImportError as e:
        print(f"❌ sentence_transformers import failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration loading"""
    print("\n⚙️  Testing configuration...")
    
    try:
        # Add app directory to path
        sys.path.append('app')
        from app.config import settings
        
        print(f"✅ Configuration loaded successfully")
        print(f"   - Pinecone Index: {settings.pinecone_index_name}")
        print(f"   - Pinecone Host: {settings.pinecone_host}")
        print(f"   - Embedding Dimension: {settings.embedding_dimension}")
        print(f"   - Gemini Model: {settings.gemini_model}")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_embedding_model():
    """Test embedding model initialization"""
    print("\n🧠 Testing embedding model...")
    
    try:
        from sentence_transformers import SentenceTransformer
        
        # Test model loading
        model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        print("✅ Embedding model loaded successfully")
        
        # Test embedding creation
        test_text = "This is a test sentence."
        embedding = model.encode(test_text)
        
        print(f"✅ Embedding created successfully")
        print(f"   - Input text length: {len(test_text)}")
        print(f"   - Embedding dimension: {len(embedding)}")
        print(f"   - Embedding type: {type(embedding)}")
        
        return True
    except Exception as e:
        print(f"❌ Embedding model test failed: {e}")
        return False

def test_environment():
    """Test environment variables"""
    print("\n🔐 Testing environment variables...")
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    required_vars = [
        'PINECONE_API_KEY',
        'GOOGLE_API_KEY'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Missing environment variables: {', '.join(missing_vars)}")
        print("   Please create a .env file with these variables:")
        for var in missing_vars:
            print(f"   {var}=your_api_key_here")
        return False
    else:
        print("✅ All required environment variables found")
        return True

def main():
    """Run all basic tests"""
    print("🚀 Basic Functionality Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_embedding_model,
        test_environment
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed! Your environment is ready.")
        print("\nNext steps:")
        print("1. Run: python test_pinecone.py")
        print("2. Run: python example_usage.py")
    else:
        print("⚠️  Some tests failed. Please fix the issues above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 