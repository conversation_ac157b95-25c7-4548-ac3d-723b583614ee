# CORS Debugging Guide

This guide will help you debug CORS issues with your FastAPI application.

## Quick Start

1. **Start the server with debugging enabled:**
   ```bash
   python start_server.py
   ```

2. **Test CORS configuration:**
   ```bash
   python test_cors.py
   ```

3. **Test frontend requests:**
   ```bash
   python test_frontend_requests.py
   ```

4. **Open the HTML test page:**
   Open `test_frontend.html` in your browser

## What I've Added for Debugging

### 1. Enhanced CORS Configuration (`app/config.py`)
- Added more common frontend development ports
- Enabled debug mode by default
- Added wildcard origin (`*`) for development

### 2. Comprehensive Debug Middleware (`app/main.py`)
- Logs all incoming requests with full details
- Tracks CORS headers in both requests and responses
- Shows processing time for each request
- Identifies if origin is in allowed list

### 3. Debug Endpoints
- `/debug/cors` - Shows current CORS configuration
- Enhanced `/` endpoint with CORS info

### 4. Test Scripts
- `test_cors.py` - Tests CORS configuration
- `test_frontend_requests.py` - Simulates frontend requests
- `test_frontend.html` - Browser-based testing

## Step-by-Step Debugging

### Step 1: Check Server Status
```bash
# Start the server
python start_server.py

# In another terminal, test if server is running
curl http://localhost:8000/health
```

### Step 2: Check CORS Configuration
```bash
# Test CORS configuration
python test_cors.py
```

### Step 3: Check Server Logs
When you make requests, the server will now log detailed information:
- Request method and URL
- All request headers
- Origin validation
- CORS response headers
- Processing time

### Step 4: Test from Browser
1. Open `test_frontend.html` in your browser
2. Click "Run All Tests" to test all endpoints
3. Check browser console for any errors

### Step 5: Check Browser Network Tab
1. Open browser developer tools
2. Go to Network tab
3. Make a request from your frontend
4. Look for:
   - OPTIONS requests (preflight)
   - CORS headers in responses
   - Any failed requests

## Common Issues and Solutions

### Issue 1: "Origin not allowed"
**Symptoms:** Browser shows CORS error, server logs show "Origin is NOT in allowed list"

**Solution:** Add your frontend origin to `cors_origins` in `app/config.py`

### Issue 2: Missing CORS headers
**Symptoms:** No `Access-Control-Allow-Origin` header in response

**Solution:** Check that CORS middleware is properly configured in `app/main.py`

### Issue 3: Preflight requests failing
**Symptoms:** OPTIONS requests return 404 or other errors

**Solution:** Ensure your FastAPI app handles OPTIONS requests properly

### Issue 4: Server not receiving requests
**Symptoms:** No logs in server console when frontend makes requests

**Possible causes:**
- Server not running
- Wrong port
- Network connectivity issues
- Firewall blocking requests

## Testing Different Frontend Ports

The current configuration supports these common frontend ports:
- `http://localhost:3000` (Vite default)
- `http://localhost:5173` (Vite alternative)
- `http://127.0.0.1:3000` (Alternative localhost)
- `http://localhost:8080` (Common dev port)
- `http://localhost:4000` (Common dev port)
- `http://localhost:5000` (Common dev port)
- `http://localhost:3001` (Alternative React port)
- `http://localhost:4200` (Angular default)
- `http://localhost:8081` (Vue CLI)
- `http://localhost:9000` (Another common port)
- `*` (Allow all origins - development only)

## Environment Variables

You can override CORS settings with environment variables:
```bash
# Set specific origins
export CORS_ORIGINS='["http://localhost:3000","http://localhost:5173"]'

# Enable debug mode
export DEBUG=true
```

## Production Considerations

For production, remove the wildcard origin (`*`) and only include your actual frontend domain:

```python
cors_origins: List[str] = [
    "https://yourdomain.com",
    "https://www.yourdomain.com"
]
```

## Next Steps

1. Start the server and check the logs
2. Run the test scripts to verify CORS is working
3. Test from your actual frontend application
4. Check browser console and network tab for any errors
5. Update CORS origins if needed

If you're still having issues, check the server logs for detailed debugging information that will help identify the specific problem. 