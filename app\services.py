# app/services.py
import pinecone
import google.generativeai as genai
from sentence_transformers import SentenceTransformer
from typing import List, Dict, Any
import logging

from app.config import settings
from app.models import SearchResult


logger = logging.getLogger(__name__)


class VectorService:
    def __init__(self):
        # Initialize Pinecone
        pinecone.init(api_key=settings.pinecone_api_key)
        self.index = pinecone.Index(settings.pinecone_index_name)
        
        # Initialize embedding model
        self.embedding_model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
    
    def create_embedding(self, text: str) -> List[float]:
        """Create embedding for text"""
        return self.embedding_model.encode(text).tolist()
    
    def upsert_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """Upsert documents to Pinecone"""
        try:
            vectors = []
            for doc in documents:
                embedding = self.create_embedding(doc['content'])
                vectors.append({
                    'id': doc['id'],
                    'values': embedding,
                    'metadata': doc['metadata']
                })
            
            self.index.upsert(vectors=vectors)
            return True
        except Exception as e:
            logger.error(f"Error upserting documents: {e}")
            return False
    
    def search(self, query: str, top_k: int = 5) -> List[SearchResult]:
        """Search for similar documents"""
        try:
            query_embedding = self.create_embedding(query)
            
            response = self.index.query(
                vector=query_embedding,
                top_k=top_k,
                include_metadata=True
            )
            
            results = []
            for match in response.matches:
                metadata = match.metadata
                result = SearchResult(
                    content=metadata.get('content', ''),
                    source=metadata.get('source', ''),
                    section=metadata.get('section', ''),
                    score=match.score,
                    document_type=metadata.get('document_type', 'other')
                )
                results.append(result)
            
            return results
        except Exception as e:
            logger.error(f"Error searching documents: {e}")
            return []


class LLMService:
    def __init__(self):
        genai.configure(api_key=settings.google_api_key)
        self.model = genai.GenerativeModel(settings.gemini_model)
    
    def generate_response(self, query: str, context: List[SearchResult]) -> str:
        """Generate response using Gemini with context"""
        try:
            # Build context from search results
            context_text = "\n\n".join([
                f"Source: {result.source} - {result.section}\n{result.content}"
                for result in context
            ])
            
            prompt = f"""You are a helpful legal assistant for Pakistani law. Answer the user's question based on the provided legal context.

Context from Pakistani legal documents:
{context_text}

User Question: {query}

Instructions:
1. Answer based only on the provided context
2. Be accurate and specific
3. Mention relevant legal sections/articles
4. If you cannot answer from the context, say so
5. Keep the answer clear and understandable for non-lawyers
6. Always cite the source (e.g., "According to PPC Section X" or "Constitution Article Y")

Answer:"""

            response = self.model.generate_content(prompt)
            return response.text
        
        except Exception as e:
            logger.error(f"Error generating LLM response: {e}")
            return "I apologize, but I encountered an error while processing your request. Please try again."


class RAGService:
    def __init__(self):
        self.vector_service = VectorService()
        self.llm_service = LLMService()
    
    def query(self, user_query: str) -> tuple[str, List[SearchResult]]:
        """Main RAG query function"""
        # Search for relevant documents
        search_results = self.vector_service.search(
            query=user_query, 
            top_k=settings.max_results
        )
        
        # Generate response using LLM
        answer = self.llm_service.generate_response(user_query, search_results)
        
        return answer, search_results
    
    def ingest_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """Ingest documents into the vector database"""
        return self.vector_service.upsert_documents(documents)