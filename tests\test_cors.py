#!/usr/bin/env python3
"""
Test script to verify CORS configuration and debug request issues
"""
import requests
import json
from app.config import settings

def test_cors_configuration():
    """Test the current CORS configuration"""
    print("=== CORS Configuration Test ===\n")
    
    # Display current CORS settings
    print("Current CORS Origins:")
    for origin in settings.cors_origins:
        print(f"  - {origin}")
    
    print(f"\nDebug mode: {settings.debug}")
    print(f"Port: {settings.port}")
    
    # Test if server is running
    try:
        base_url = f"http://localhost:{settings.port}"
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"\n✅ Server is running at {base_url}")
        print(f"Health check response: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print(f"\n❌ Server is not running at {base_url}")
        print("Please start the server first using: python start_server.py")
        return
    except Exception as e:
        print(f"\n❌ Error connecting to server: {e}")
        return
    
    # Test CORS preflight request
    print("\n=== Testing CORS Preflight ===")
    
    for origin in settings.cors_origins:
        try:
            # Test OPTIONS request (preflight)
            headers = {
                'Origin': origin,
                'Access-Control-Request-Method': 'POST',
                'Access-Control-Request-Headers': 'Content-Type'
            }
            
            response = requests.options(
                f"{base_url}/api/v1/chat",
                headers=headers,
                timeout=5
            )
            
            print(f"\nTesting origin: {origin}")
            print(f"  Status: {response.status_code}")
            print(f"  Access-Control-Allow-Origin: {response.headers.get('Access-Control-Allow-Origin', 'Not set')}")
            print(f"  Access-Control-Allow-Methods: {response.headers.get('Access-Control-Allow-Methods', 'Not set')}")
            print(f"  Access-Control-Allow-Headers: {response.headers.get('Access-Control-Allow-Headers', 'Not set')}")
            
            if response.status_code == 200:
                print("  ✅ CORS preflight successful")
            else:
                print("  ❌ CORS preflight failed")
                
        except Exception as e:
            print(f"\n❌ Error testing origin {origin}: {e}")
    
    # Test actual request
    print("\n=== Testing Actual Request ===")
    
    for origin in settings.cors_origins:
        try:
            headers = {
                'Origin': origin,
                'Content-Type': 'application/json'
            }
            
            data = {
                "query": "test query",
                "conversation_id": "test-123"
            }
            
            response = requests.post(
                f"{base_url}/api/v1/chat",
                headers=headers,
                json=data,
                timeout=10
            )
            
            print(f"\nTesting origin: {origin}")
            print(f"  Status: {response.status_code}")
            print(f"  Access-Control-Allow-Origin: {response.headers.get('Access-Control-Allow-Origin', 'Not set')}")
            
            if response.status_code in [200, 400, 500]:  # Valid responses
                print("  ✅ CORS request successful")
            else:
                print("  ❌ CORS request failed")
                
        except Exception as e:
            print(f"\n❌ Error testing origin {origin}: {e}")

if __name__ == "__main__":
    test_cors_configuration()
