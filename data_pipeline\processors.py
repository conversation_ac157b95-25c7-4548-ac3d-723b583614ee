import re
from typing import List, Dict
import PyPDF2
from docx import Document

from app.models import DocumentType


class DocumentProcessor:
    def __init__(self, chunk_size: int = 512, chunk_overlap: int = 50):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def process_pdf(self, file_path: str) -> str:
        """Extract text from PDF"""
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    
    def process_docx(self, file_path: str) -> str:
        """Extract text from DOCX"""
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    
    def process_txt(self, file_path: str) -> str:
        """Process plain text file"""
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep legal formatting
        text = re.sub(r'[^\w\s\.\,\:\;\(\)\-\[\]]', '', text)
        
        return text.strip()
    
    def chunk_text(self, text: str) -> List[str]:
        """Split text into overlapping chunks"""
        words = text.split()
        chunks = []
        
        for i in range(0, len(words), self.chunk_size - self.chunk_overlap):
            chunk_words = words[i:i + self.chunk_size]
            chunk = ' '.join(chunk_words)
            chunks.append(chunk)
            
            if i + self.chunk_size >= len(words):
                break
        
        return chunks
    
    def extract_legal_sections(self, text: str, document_type: DocumentType) -> List[Dict[str, str]]:
        """Extract legal sections based on document type"""
        sections = []
        
        if document_type == DocumentType.PENAL_CODE:
            # Extract PPC sections (Section XXX. Title)
            pattern = r'(Section\s+\d+[A-Z]*\.?\s*[^\n]+?)(?=Section\s+\d+|$)'
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            
            for match in matches:
                section_text = match.group(1).strip()
                section_num = re.search(r'Section\s+(\d+[A-Z]*)', section_text, re.IGNORECASE)
                
                if section_num:
                    sections.append({
                        'section': f"Section {section_num.group(1)}",
                        'content': section_text
                    })
        
        elif document_type == DocumentType.CONSTITUTION:
            # Extract Constitutional articles
            pattern = r'(Article\s+\d+[A-Z]*\.?\s*[^\n]+?)(?=Article\s+\d+|$)'
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            
            for match in matches:
                article_text = match.group(1).strip()
                article_num = re.search(r'Article\s+(\d+[A-Z]*)', article_text, re.IGNORECASE)
                
                if article_num:
                    sections.append({
                        'section': f"Article {article_num.group(1)}",
                        'content': article_text
                    })
        
        return sections