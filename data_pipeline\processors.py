import re
from typing import List, Dict
import PyPDF2
from docx import Document

from app.models import DocumentType


class DocumentProcessor:
    def __init__(self, chunk_size: int = 512, chunk_overlap: int = 50):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
    
    def process_pdf(self, file_path: str) -> str:
        """Extract text from PDF"""
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text
    
    def process_docx(self, file_path: str) -> str:
        """Extract text from DOCX"""
        doc = Document(file_path)
        text = ""
        for paragraph in doc.paragraphs:
            text += paragraph.text + "\n"
        return text
    
    def process_txt(self, file_path: str) -> str:
        """Process plain text file"""
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove special characters but keep legal formatting
        text = re.sub(r'[^\w\s\.\,\:\;\(\)\-\[\]]', '', text)
        
        return text.strip()
    
    def chunk_text(self, text: str) -> List[str]:
        """Split text into overlapping chunks"""
        words = text.split()
        chunks = []
        
        for i in range(0, len(words), self.chunk_size - self.chunk_overlap):
            chunk_words = words[i:i + self.chunk_size]
            chunk = ' '.join(chunk_words)
            chunks.append(chunk)
            
            if i + self.chunk_size >= len(words):
                break
        
        return chunks
    
    def extract_legal_sections(self, text: str, document_type: DocumentType) -> List[Dict[str, str]]:
        """Extract legal sections based on document type with improved patterns"""
        sections = []

        if document_type == DocumentType.PENAL_CODE:
            # Improved patterns for PPC sections
            # Pattern 1: Standard sections (Section 123. Title)
            pattern1 = r'(\d+\.\s*[^\n]+?)(?=\n\s*\d+\.|$)'
            # Pattern 2: Sections with explicit "Section" keyword
            pattern2 = r'(Section\s+\d+[A-Z]*\.?\s*[^\n]+?)(?=Section\s+\d+|$)'

            # Try pattern 1 first (more common in PPC)
            matches = list(re.finditer(pattern1, text, re.IGNORECASE | re.DOTALL))

            if len(matches) < 10:  # If we don't find many sections, try pattern 2
                matches = list(re.finditer(pattern2, text, re.IGNORECASE | re.DOTALL))

            for match in matches:
                section_text = match.group(1).strip()

                # Extract section number
                section_num_match = re.search(r'(\d+[A-Z]*)', section_text)
                if section_num_match:
                    section_num = section_num_match.group(1)

                    # Clean up the section text
                    cleaned_text = self._clean_section_text(section_text)

                    if len(cleaned_text) > 50:  # Only include substantial sections
                        sections.append({
                            'section': f"Section {section_num}",
                            'content': cleaned_text
                        })

        elif document_type == DocumentType.CONSTITUTION:
            # Improved patterns for Constitutional articles
            # Pattern 1: Article with number and title
            pattern1 = r'(Article\s+\d+[A-Z]*\.?\s*[^\n]+?)(?=Article\s+\d+|$)'
            # Pattern 2: Just numbered articles
            pattern2 = r'(\d+\.\s*[^\n]+?)(?=\n\s*\d+\.|$)'

            matches = list(re.finditer(pattern1, text, re.IGNORECASE | re.DOTALL))

            # If no explicit "Article" found, try numbered pattern
            if len(matches) < 5:
                matches = list(re.finditer(pattern2, text, re.IGNORECASE | re.DOTALL))

            for match in matches:
                article_text = match.group(1).strip()

                # Extract article number
                article_num_match = re.search(r'(?:Article\s+)?(\d+[A-Z]*)', article_text, re.IGNORECASE)
                if article_num_match:
                    article_num = article_num_match.group(1)

                    # Clean up the article text
                    cleaned_text = self._clean_section_text(article_text)

                    if len(cleaned_text) > 50:  # Only include substantial articles
                        sections.append({
                            'section': f"Article {article_num}",
                            'content': cleaned_text
                        })

        print(f"Extracted {len(sections)} legal sections")
        return sections

    def _clean_section_text(self, text: str) -> str:
        """Clean and format legal section text"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove page numbers and headers/footers
        text = re.sub(r'Page\s+\d+', '', text, flags=re.IGNORECASE)
        text = re.sub(r'PAKISTAN\s+PENAL\s+CODE', '', text, flags=re.IGNORECASE)
        text = re.sub(r'CONSTITUTION\s+OF\s+PAKISTAN', '', text, flags=re.IGNORECASE)

        # Clean up formatting artifacts
        text = re.sub(r'[^\w\s\.\,\:\;\(\)\-\[\]\"\'\/]', '', text)

        return text.strip()