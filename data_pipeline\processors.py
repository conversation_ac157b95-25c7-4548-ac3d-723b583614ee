import re
from typing import List
import PyPDF2


class DocumentProcessor:
    """Simple document processor for extracting and chunking text from legal documents"""

    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 100):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        text = ""
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
        return text

    def clean_text(self, text: str) -> str:
        """Clean and normalize extracted text"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove page headers/footers
        text = re.sub(r'Page\s+\d+', '', text, flags=re.IGNORECASE)
        return text.strip()

    def chunk_text(self, text: str, document_type: str = "general") -> List[str]:
        """Smart chunking that respects legal document structure"""

        # First try to extract legal sections
        if document_type in ["penal_code", "constitution"]:
            legal_chunks = self._extract_legal_sections(text, document_type)
            if legal_chunks:
                return legal_chunks

        # Fall back to semantic chunking
        return self._semantic_chunk(text)

    def _extract_legal_sections(self, text: str, document_type: str) -> List[str]:
        """Extract natural legal sections (Articles, Sections, etc.)"""
        sections = []

        if document_type == "penal_code":
            # Look for PPC sections
            patterns = [
                r'(\d+\.\s+[^0-9]+?)(?=\d+\.|$)',  # "123. Section title and content"
                r'(Section\s+\d+[A-Z]*\.?\s+[^S]+?)(?=Section\s+\d+|$)'  # "Section 123. Content"
            ]
        elif document_type == "constitution":
            # Look for constitutional articles
            patterns = [
                r'(Article\s+\d+[A-Z]*\.?\s+[^A]+?)(?=Article\s+\d+|$)',  # "Article 8. Content"
                r'(\d+\.\s+[^0-9]+?)(?=\d+\.|$)'  # "8. Article content"
            ]
        else:
            return []

        for pattern in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
            for match in matches:
                section_text = match.group(1).strip()
                if len(section_text) > 100:  # Only substantial sections
                    sections.append(self._clean_section(section_text))

        # If we found good sections, return them
        if len(sections) > 5:
            print(f"Found {len(sections)} legal sections")
            return sections

        return []

    def _semantic_chunk(self, text: str) -> List[str]:
        """Chunk text respecting sentence and paragraph boundaries"""
        chunks = []

        # Split into paragraphs first
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]

        current_chunk = ""
        current_word_count = 0

        for paragraph in paragraphs:
            paragraph_words = len(paragraph.split())

            # If paragraph alone is too big, split it by sentences
            if paragraph_words > self.chunk_size:
                # Save current chunk if it exists
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = ""
                    current_word_count = 0

                # Split large paragraph by sentences
                sentence_chunks = self._split_by_sentences(paragraph)
                chunks.extend(sentence_chunks)

            # If adding this paragraph would exceed chunk size
            elif current_word_count + paragraph_words > self.chunk_size:
                # Save current chunk
                if current_chunk:
                    chunks.append(current_chunk.strip())

                # Start new chunk with overlap
                overlap_text = self._get_overlap(current_chunk)
                current_chunk = overlap_text + "\n\n" + paragraph if overlap_text else paragraph
                current_word_count = len(current_chunk.split())

            else:
                # Add paragraph to current chunk
                current_chunk += "\n\n" + paragraph if current_chunk else paragraph
                current_word_count += paragraph_words

        # Add final chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        print(f"Created {len(chunks)} semantic chunks")
        return chunks

    def _split_by_sentences(self, text: str) -> List[str]:
        """Split large text by sentences when paragraphs are too big"""
        # Simple sentence splitting (can be improved with nltk)
        sentences = re.split(r'[.!?]+\s+', text)
        chunks = []
        current_chunk = ""

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            sentence_words = len(sentence.split())
            current_words = len(current_chunk.split())

            if current_words + sentence_words > self.chunk_size and current_chunk:
                chunks.append(current_chunk.strip())
                current_chunk = sentence
            else:
                current_chunk += ". " + sentence if current_chunk else sentence

        if current_chunk.strip():
            chunks.append(current_chunk.strip())

        return chunks

    def _get_overlap(self, text: str) -> str:
        """Get overlap text from the end of current chunk"""
        words = text.split()
        if len(words) <= self.chunk_overlap:
            return text

        overlap_words = words[-self.chunk_overlap:]
        return ' '.join(overlap_words)

    def _clean_section(self, text: str) -> str:
        """Clean extracted legal section"""
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        # Remove page artifacts
        text = re.sub(r'Page\s+\d+', '', text, flags=re.IGNORECASE)
        text = re.sub(r'PAKISTAN\s+PENAL\s+CODE', '', text, flags=re.IGNORECASE)
        text = re.sub(r'CONSTITUTION\s+OF\s+PAKISTAN', '', text, flags=re.IGNORECASE)
        return text.strip()