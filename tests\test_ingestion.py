#!/usr/bin/env python3
"""
Test script for document ingestion with updated Pinecone configuration
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add data_pipeline directory to path
sys.path.append('data_pipeline')

from document_ingester import DocumentIngester

def test_ingestion():
    """Test document ingestion with the new Pinecone configuration"""
    print("🚀 Testing Document Ingestion with Updated Pinecone Configuration")
    print("=" * 70)
    
    try:
        # Initialize the ingester
        print("📋 Initializing DocumentIngester...")
        ingester = DocumentIngester()
        print("✅ DocumentIngester initialized successfully")
        
        # Test with a small sample document
        print("\n📄 Testing with sample document...")
        
        # Create a test document path (you can modify this to test with actual files)
        test_file_path = "data/raw/fundamental_rights_from_constituion.pdf"
        
        if os.path.exists(test_file_path):
            print(f"📖 Found test file: {test_file_path}")
            
            # Test ingestion
            success = ingester.ingest_pdf(
                test_file_path,
                "Test Constitution Document",
                "constitution"
            )
            
            if success:
                print("✅ Document ingestion test successful!")
                return True
            else:
                print("❌ Document ingestion test failed")
                return False
        else:
            print(f"⚠️  Test file not found: {test_file_path}")
            print("   Skipping actual ingestion test")
            
            # Test the ingester initialization and metadata structure
            print("\n🔍 Testing metadata structure...")
            
            # Create a mock document to test metadata structure
            mock_chunk = "This is a test chunk about fundamental rights in Pakistan."
            chunk_type = ingester._identify_chunk_type(mock_chunk, "constitution")
            article_info = ingester._extract_article_info(mock_chunk)
            
            print(f"   - Chunk type: {chunk_type}")
            print(f"   - Article info: {article_info}")
            print("✅ Metadata structure test passed")
            
            return True
            
    except Exception as e:
        print(f"❌ Error during ingestion test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pinecone_connection():
    """Test Pinecone connection from the ingester"""
    print("\n🔍 Testing Pinecone Connection from DocumentIngester...")
    
    try:
        ingester = DocumentIngester()
        
        # Test basic connection
        if hasattr(ingester.pinecone, 'index') and ingester.pinecone.index:
            print("✅ Pinecone index connected successfully")
            
            # Test embedding creation
            test_text = "Test document about Pakistani law"
            embedding = ingester.pinecone.create_embedding(test_text)
            
            if embedding and len(embedding) > 0:
                print(f"✅ Embedding created successfully (dimension: {len(embedding)})")
                return True
            else:
                print("❌ Failed to create embedding")
                return False
        else:
            print("❌ Pinecone index not available")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Pinecone connection: {e}")
        return False

def main():
    """Run all ingestion tests"""
    print("🚀 Document Ingestion Test Suite")
    print("=" * 50)
    
    tests = [
        test_pinecone_connection,
        test_ingestion
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All ingestion tests passed! New Pinecone configuration is working.")
    else:
        print("⚠️  Some tests failed. Please check your configuration.")
    
    return passed == total

if __name__ == "__main__":
    main() 