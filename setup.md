# Setup Instructions

## 1. Create Project Structure
```
mkdir legal-rag-backend
cd legal-rag-backend
```
#### Create directories
```bash
mkdir -p app data/raw data/processed data_pipeline
```
#### Create __init__.py files
```bash
touch app/__init__.py data_pipeline/__init__.py
```
#### Create .gitkeep files for empty directories
```bash
touch data/raw/.gitkeep data/processed/.gitkeep
```
## 2. Setup Virtual Environment
```
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```
## 3. Install Dependencies
```
pip install -r requirements.txt
```
## 4. Setup Environment Variables
```
cp .env.example .env
```
#### Edit .env with your actual API keys

## 5. Setup Pinecone Index (One-time setup)
python -c
```python
import pinecone
from app.config import settings

pinecone.init(api_key=settings.pinecone_api_key)

# Create index if it doesn't exist
if settings.pinecone_index_name not in pinecone.list_indexes():
    pinecone.create_index(
        name=settings.pinecone_index_name,
        dimension=384,  # for sentence-transformers/all-MiniLM-L6-v2
        metric='cosine'
    )
    print('Index created successfully!')
else:
    print('Index already exists')

```

## 6. Ingest Your Legal Documents
#### Put your legal documents in data/raw/
#### Then run the ingestion script:
```bash
python data_pipeline/ingest.py
```
## 7. Run the Server
```
python app/main.py
```
#### Or with uvicorn
```
uvicorn app.main:app --reload --port 8000
```
## 8. Test the API
#### Health check
```bash
curl http://localhost:8000/health
```
#### Chat endpoint
```bash
curl -X POST http://localhost:8000/api/v1/chat \
  -H "Content-Type: application/json" \
  -d '{"query": "What is the right to self defense in Pakistan?"}'
```
## 9. View API Documentation
#### Open browser: http://localhost:8000/docs