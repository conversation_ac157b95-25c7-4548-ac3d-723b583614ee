"""
Legal RAG API - Pakistani Legal Document Retrieval-Augmented Generation System

A FastAPI-based application for querying Pakistani legal documents using
Pinecone vector database and Google Gemini LLM.
"""

__version__ = "1.0.0"
__author__ = "Legal RAG Team"
__description__ = "Pakistani Legal Document RAG System"

from .config import settings
from .models import *
from .services import RAGService

__all__ = [
    "settings",
    "RAGService"
]
