import os
from typing import List, Dict, Any
from pinecone import Pinecone
from sentence_transformers import SentenceTransformer
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class PineconeService:
    """Simple service to handle Pinecone vector database operations"""
    
    def __init__(self):
        api_key = os.getenv('PINECONE_API_KEY')
        index_name = os.getenv('PINECONE_INDEX_NAME', 'legal-rag-index-2')
        
        if not api_key:
            raise ValueError("PINECONE_API_KEY environment variable is required")
        
        print("Connecting to Pinecone...")
        # Use new Pinecone API
        self.pc = Pinecone(api_key=api_key)
        self.index = self.pc.Index(index_name)
        
        print("Loading embedding model...")
        self.embedding_model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')
        print("Ready!")
    
    def create_embedding(self, text: str) -> List[float]:
        """Create embedding for text"""
        return self.embedding_model.encode(text).tolist()
    
    def upsert_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """Upload documents to Pinecone"""
        try:
            vectors = []
            for doc in documents:
                embedding = self.create_embedding(doc['content'])
                vectors.append({
                    'id': doc['id'],
                    'values': embedding,
                    'metadata': doc['metadata']
                })
            
            # Upload in batches
            batch_size = 100
            for i in range(0, len(vectors), batch_size):
                batch = vectors[i:i + batch_size]
                self.index.upsert(vectors=batch)
                print(f"Uploaded batch {i//batch_size + 1}")
            
            return True
        except Exception as e:
            print(f"Error uploading: {e}")
            return False
